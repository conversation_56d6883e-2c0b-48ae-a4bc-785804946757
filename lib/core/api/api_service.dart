import 'dart:typed_data';
import 'package:dio/dio.dart';
import 'package:the_voice_directory_flutter/core/api/api_connectivity.dart';
import 'package:the_voice_directory_flutter/core/api/api_endpoints.dart';
import 'package:the_voice_directory_flutter/core/api/api_params.dart';
import 'package:the_voice_directory_flutter/core/api/api_service_interface.dart';
import 'package:the_voice_directory_flutter/features/apply_job/voice_apply_job/data/apply_job_rq_model.dart';
import 'package:the_voice_directory_flutter/features/common/upload_media/data/pre_signed_url_req_model.dart';
import 'package:the_voice_directory_flutter/features/google_calender/data/request_model.dart';
import 'package:the_voice_directory_flutter/utils/common_models/user_info_req_model.dart';
import 'package:the_voice_directory_flutter/utils/common_models/api_response_model.dart';
import 'package:the_voice_directory_flutter/utils/string_constants/app_strings.dart';

import '../../features/explore_artists/data/artist_filter_selection.dart';
import '../../features/explore_artists/data/artist_filter_type.dart';
import '../../features/jobs/enums/application_status.dart';
import '../../features/landing_page/data/model/contact_us_form_model.dart';
import '../../features/post_job/models/job_post_model.dart';
import '../../utils/common_models/send_notification_req_model.dart';
import '../../utils/environment_config.dart';

class ApiService extends APIConnectivity implements ApiServiceInterface {
  static ApiService? _apiService;

  ApiService._internal() : super(baseURL: EnvironmentConfig.apiBaseUrl);

  static void _initApiService() {
    if (_apiService == null) {
      _apiService = ApiService._internal();
    } else {
      throw StateError("API service is already initialised");
    }
  }

  static ApiService get instance {
    if (_apiService == null) {
      _initApiService();
    }
    return _apiService!;
  }

  @override
  Future<ApiResponse> signUp(
      {required String email,
      String? deviceId,
      String? registrationId,
      String? deviceName,
      String? deviceOS}) async {
    return await apiRequest(
        request: dio.post(ApiEndpoints.signUp(), data: {
      Params.email: email,
      Params.deviceId: deviceId,
      Params.deviceName: deviceName,
      Params.registrationId: registrationId
    }));
  }

  @override
  Future<ApiResponse> login(
      {required String email,
      String? deviceId,
      String? registrationId,
      String? deviceName,
      String? deviceOS}) async {
    return await apiRequest(
        request: dio.post(ApiEndpoints.login(), data: {
      Params.email: email,
      Params.deviceId: deviceId,
      Params.deviceName: deviceName,
      Params.registrationId: registrationId
    }));
  }

  @override
  Future<ApiResponse> loginWithGoogle({
   // required String? accessToken,
   // required String? idToken,
    required String? serverAuthCode,
    String? deviceId,
    String? registrationId,
    String? deviceName,
    String? deviceOS,
  }) async {
    return await apiRequest(
      request: dio.post(
        ApiEndpoints.socialLogin(),
        data: {
          Params.provider: "google",
          // if (accessToken != null && accessToken.isNotEmpty)
          //   "token": accessToken
          // else
          //   "id_token": idToken,
          if (serverAuthCode != null) Params.serverAuthCode: serverAuthCode,
          if (deviceId != null) Params.deviceId: deviceId,
          if (deviceName != null) Params.deviceType: deviceName,
          if (registrationId != null) Params.registrationId: registrationId
        },
      ),
    );
  }

  @override
  Future<ApiResponse> loginWithApple({
    required String? token,
    String? firstName,
    String? lastName,
    String? deviceId,
    String? registrationId,
    String? deviceName,
    String? deviceOS,
  }) async {
    return await apiRequest(
      request: dio.post(
        ApiEndpoints.socialLogin(),
        data: {
          Params.provider: "apple",
          Params.token: token,
          if (firstName != null && firstName.isNotEmpty) Params.firstName: firstName,
          if (lastName != null && lastName.isNotEmpty) Params.lastName: lastName,
          if (deviceId != null) Params.deviceId: deviceId,
          if (deviceName != null) Params.deviceType: deviceName,
          if (registrationId != null) Params.registrationId: registrationId
        },
      ),
    );
  }

  @override
  Future<ApiResponse> verifyOtp({
    required int otp,
    String? email,
    required String type,
    String? phoneNumber,
    String? playerId,
  }) async {
    return await apiRequest(
        request: dio.post(ApiEndpoints.verifyOtp(), data: {
      Params.otp: otp,
      if (email != null && email.isNotEmpty) Params.email: email,
      Params.type: type,
      if (phoneNumber != null && phoneNumber.isNotEmpty)
        Params.phoneNumber: phoneNumber,
      if (playerId != null && playerId.isNotEmpty) Params.playerId: playerId,
    }));
  }

  @override
  Future<ApiResponse> resendOtp({
    String? email,
    String? phoneNumber,
  }) async {
    return await apiRequest(
        request: dio.post(ApiEndpoints.resendOtp(), data: {
      if (email != null && email.isNotEmpty) Params.email: email,
      if (phoneNumber != null && phoneNumber.isNotEmpty)
        Params.phoneNumber: phoneNumber
    }));
  }

  @override
  Future<ApiResponse> submitUserInfoData(
      {required UserInfoRequestModel userInfoRequestModel}) async {
    return await apiRequest(
        request: dio.patch(ApiEndpoints.user(),
            data: userInfoRequestModel.toJson()));
  }

  @override
  Future<ApiResponse> getUserData({int? id}) async {
    return await apiRequest(
        request: dio.get(
      ApiEndpoints.user(id: id),
    ));
  }

  @override
  Future<ApiResponse> dropDowns({bool? isFlexible}) async {
    if (isFlexible != null) {
      return await apiRequest( request: dio.get(ApiEndpoints.dropDowns(),
              queryParameters: {Params.isFlexible: isFlexible}));
    }
    return await apiRequest(request: dio.get(ApiEndpoints.dropDowns()));
  }

  @override
  Future<ApiResponse> getPreSignedUrl({
    required PreSignedUrlReqModel preSignedUrlReqModel,
  }) async {
    return await apiRequest(
        request: dio.post(ApiEndpoints.getPreSignedUrl(),
            data: preSignedUrlReqModel.toJson()));
  }

  @override
  Future<ApiResponse> uploadMedia({
    required String fileUrl,
    required Uint8List uploadFile,
    required String contentType,
  }) async {
    return await apiRequest(
        request: Dio().put(
      fileUrl,
      options: Options(headers: {
        Headers.contentLengthHeader: uploadFile.length,
        Headers.contentTypeHeader: contentType
      }),
      data: Stream.fromIterable(uploadFile.map((e) => [e])),
    ));
  }

  @override
  Future<ApiResponse> logout() async {
    return await apiRequest(request: dio.delete(ApiEndpoints.logout()));
  }

  @override
  Future<ApiResponse> getApplicantList(int jobId, int filterId) async {
    final Map<String, dynamic> queryParameters = {};
    if (filterId != 0) {
      queryParameters[Params.applicationStatus] = filterId;
    }
    return await apiRequest(
      request: dio.get(
        ApiEndpoints.applicantList(jobId),
        queryParameters: queryParameters,
      ),
    );
  }

  @override
  Future<ApiResponse> getJobDetail(int? id) async {
    return await apiRequest(
        request: dio.get(
      ApiEndpoints.getJobDetails(id),
    ));
  }

    @override
  Future<ApiResponse> getUsersList({required String searchName}) async {
    return await apiRequest(
      request: dio.get(
        ApiEndpoints.getUsersList(),
        queryParameters: {
          'search': searchName,
        },
      ),
    );
  }

  @override
  Future<ApiResponse> postJob({required JobPostModel jobPostModel}) async {
    return await apiRequest(
      request: dio.post(
        ApiEndpoints.postJob(),
        data: jobPostModel.toJson(),
      ),
    );
  }

  @override
  Future<ApiResponse> getAllJobs({
    String? searchName,
    String? filter,
    String? sortOrder,
    required bool isClient,
  }) async {
    return await apiRequest(
      request: dio.get(
        ApiEndpoints.getAllJobs(isClient: isClient),
        queryParameters: {
          if (searchName != null) 'search': searchName,
          if (filter != null) 'status': filter,
          if (sortOrder != null) 'sorted_by': sortOrder,
        },
      ),
    );
  }

  @override
  Future<ApiResponse> updateJobDetails({
    required int id,
    required JobPostModel jobPostModel,
  }) async {
    return await apiRequest(
      request: dio.patch(
        "${ApiEndpoints.getAllJobs(isClient: true)}$id/",
        data: jobPostModel.toJson(),
      ),
    );
  }

  @override
  Future<ApiResponse> getApplicationData(int? id) async {
    return await apiRequest(
        request: dio.get(
      ApiEndpoints.applicationDetail(id),
    ));
  }

  @override
  Future<ApiResponse> deleteJob(int? id) async {
    return await apiRequest(request: dio.delete(ApiEndpoints.deleteJob(id)));
  }

  @override
  Future<ApiResponse> applyJobData(
      {required ApplyJobRequestModel applyJobRequestModel}) async {
  return await apiRequest(request: dio.post(ApiEndpoints.applyJob(),
        data: applyJobRequestModel.toJson()));
  }

  @override
  Future<ApiResponse> shortlistCandidateApplication(int? applicantId) async {
    return await apiRequest(
      request: dio.patch(
        ApiEndpoints.shortlistCandidateApplication(applicantId),
        data: {
          Params.applicationStatus: ApplicationStatus.shortlisted.toString(),
        },
      ),
    );
  }

  @override
  Future<ApiResponse> postJobReview({
    required int jobId,
    required int rating,
    required String feedback,
  }) async {
    return await apiRequest(
      request: dio.post(
        ApiEndpoints.jobReview(),
        data: {
          Params.job: jobId,
          Params.rating: rating,
          Params.feedback: feedback,
        },
      ),
    );
  }
    @override
  Future<ApiResponse> paymentMethod(int? jobId, {String? amount}) async {
    return await apiRequest(
      request: dio.post(
        ApiEndpoints.paymentMethod(amount: amount),
        data: {
          Params.job: jobId,
          if (amount != null) Params.amount: amount,
          (amount != null ? Params.paymentMethod : Params.voicePaymentMethod): AppStrings.offline,
        },
      ),
    );
  }

  @override
  Future<ApiResponse> acceptShortlistedApplication(int? applicantId) async {
    return await apiRequest(
      request: dio.patch(
        ApiEndpoints.shortlistCandidateApplication(applicantId),
        data: {
          Params.applicationStatus: "Accepted",
        },
      ),
    );
  }

  @override
  Future<ApiResponse> getArtists({
    String? searchName,
    Map<String, dynamic>? location,
    required ArtistFilterSelection filters,
    required bool isFavorite,
     String?  selectedTab,
  }) async {
    Map<String, dynamic> queryParameters = {};
    if (searchName != null) {
      queryParameters[Params.search] = searchName;
    }

    void addParameter(String paramName, List<int>? ids) {
      if (ids != null && ids.isNotEmpty) {
        queryParameters[paramName] = ids.join(',');
      }
    }

    addParameter(ArtistFilterType.gender.apiParamName, filters.genderIds);
    addParameter(ArtistFilterType.projectType.apiParamName, filters.projectTypeIds);
    addParameter(ArtistFilterType.ageRange.apiParamName, filters.ageRangeIds);
    addParameter(ArtistFilterType.language.apiParamName, filters.languageIds);
    addParameter(ArtistFilterType.accent.apiParamName, filters.accentIds);
    addParameter(ArtistFilterType.experience.apiParamName, filters.experienceIds);
    addParameter(ArtistFilterType.services.apiParamName, filters.servicesIds);

    if (isFavorite) {
      queryParameters[Params.type] = Params.favorite;
    }
    if (location != null) {
      queryParameters[Params.lat] = location['location']?['lat'];
      queryParameters[Params.lng] = location['location']?['lng'];
    }
    
    if (selectedTab == 'voice') {
      queryParameters[Params.userType] = 'voice';
    } else if (selectedTab == 'ancillary') {
      queryParameters[Params.userType] = 'ancillary';
    }

    return await apiRequest(
      request: dio.get(
        ApiEndpoints.getVoiceArtists(),
        queryParameters: queryParameters,
      ),
    );
  }
  
  @override
  Future<ApiResponse> getNotificationList(bool? isPrivate) async {
    return await apiRequest(
        request: dio.get(
      ApiEndpoints.getNotificationList(isPrivate),
    ));
  }

  @override
  Future<ApiResponse> readNotification(int? id, String? isReadType) async {
    return await apiRequest(
        request: dio.patch(
      ApiEndpoints.readNotification(id),
      data: {
          Params.type: isReadType == 'private' ? 'private' : 'others',
        },
    ));
  }
  
  @override
  Future<ApiResponse> getNotificationCount() async {
    return await apiRequest(
        request: dio.get(
      ApiEndpoints.getNotificationCount(),
    ));
  }

  @override
  Future<ApiResponse> getReviewList(int? id) async {
    return await apiRequest(
        request: dio.get(
      ApiEndpoints.getReviewList(id),
    ));
  }

  @override
  Future<ApiResponse> deleteAccount() async {
    return await apiRequest(request: dio.delete(ApiEndpoints.deleteAccount()));
  }

  
  @override
  Future<ApiResponse> favoriteArtist(int artistId, bool isFavorite) async {
    return await apiRequest(
      request: dio.post(
        ApiEndpoints.favoriteArtist(artistId),
        data: {
          Params.isFavorite: isFavorite,
        },
      ),
    );
  }
 @override
  Future<ApiResponse> favoriteJob(int jobId) async {
    return await apiRequest(
      request: dio.post(
        ApiEndpoints.favoriteJob(),
        data: {
          Params.job: jobId,
        },
      ),
    );
  }

  @override
  Future<ApiResponse> getCalenderEvent({
    required String userId,
    required String startDate,
    required String endDate,
  }) async {
    return await apiRequest(
      request: dio.get(
        ApiEndpoints.getCalenderEvent(userId),
        queryParameters: {
          'time_min': startDate,
          'time_max': endDate
        }
      )
    );
  }

  @override
  Future<ApiResponse> bookCalenderEvent(
    {required RequestModel requestModel}) async {
    return await apiRequest(
        request: dio.post(ApiEndpoints.bookCalenderEvent(),
            data: requestModel.toJson()));
  }

  @override
  Future<ApiResponse> getServerAuthCode({
    required String? serverAuthCode, String? deviceType,
  }) async {
    return await apiRequest(
      request: dio.post(ApiEndpoints.getServerAuthCode(), data: {
      Params.serverAuthCode: serverAuthCode,
      Params.deviceType: deviceType,
      Params.provider: "google"
    }));
  }
   @override
  Future<ApiResponse> disconnectCalendar() async {
    return await apiRequest(
      request: dio.post(
        ApiEndpoints.disconnectCalender(),
      ),
    );
  }
  @override
  Future<ApiResponse> googleAddress(String? address ) async {
    return await apiRequest(
      request: dio.post(
        ApiEndpoints.getGoogleAddress(),
        data: {
          Params.address: address,
        },
      ),
    );
  }

  @override
  Future<ApiResponse> sendNotification({required SendNoticationReqModel sendNoticationReqModel}) async {
    return await apiRequest(
      request: dio.post(ApiEndpoints.sendNotification(), data: sendNoticationReqModel.toJson()),
    );
  }
  
  @override
  Future<ApiResponse> report(int id,String comment) async {
    return await apiRequest(
        request: dio.post(
          ApiEndpoints.report(id),
      data: {
        Params.description: comment,
      },
    ));
  }
 
  @override
  Future<ApiResponse> updateJobInvitees({
    required int id,
    required List<int> inviteeIds,
  }) async {
    return await apiRequest(
      request: dio.patch(
        "${ApiEndpoints.getAllJobs(isClient: true)}$id/",
        data: {
          Params.associatedUsers: inviteeIds,
        },
      ),
    );
  }
  
  @override
  Future<ApiResponse> getInvitedUserList(int? id) async {
    return await apiRequest(
        request: dio.get(
      ApiEndpoints.getInvitedUserList(id),
    ));
  }

  @override
  Future<ApiResponse> explorePublicVoices({
    String? searchName,
    ArtistFilterSelection? filters,
  }) async {
    Map<String, dynamic> queryParameters = {};
    if (searchName != null) {
      queryParameters[Params.search] = searchName;
    }

    void addParameter(String paramName, List<int>? ids) {
      if (ids != null && ids.isNotEmpty) {
        queryParameters[paramName] = ids.join(',');
      }
    }

    addParameter(ArtistFilterType.gender.apiParamName, filters?.genderIds);
    addParameter(ArtistFilterType.projectType.apiParamName, filters?.projectTypeIds);
    addParameter(ArtistFilterType.ageRange.apiParamName, filters?.ageRangeIds);
    addParameter(ArtistFilterType.language.apiParamName, filters?.languageIds);
    addParameter(ArtistFilterType.accent.apiParamName, filters?.accentIds);
    addParameter(ArtistFilterType.experience.apiParamName, filters?.experienceIds);

    return await apiRequest(
      request: dio.get(
        ApiEndpoints.explorePublicVoices(),
        queryParameters: queryParameters,
      ),
    );
  }

  @override
  Future<ApiResponse> topPublicVoices() async {
    return await apiRequest(
      request: dio.get(
        ApiEndpoints.topPublicVoices(),
      ),
    );
  }

  @override
  Future<ApiResponse> getPublicVoiceProfileData({int? id}) async {
    return await apiRequest(
      request: dio.get(
        ApiEndpoints.explorePublicVoices(id: id),
      ),
    );
  }

  @override
  Future<ApiResponse> contactSupport({
    required ContactUsFormModel contactUsFormModel,
  }) async {
    return await apiRequest(
      request: dio.post(
        ApiEndpoints.contactSupport(),
        data: contactUsFormModel.toJson(),
      ),
    );
  }
}
