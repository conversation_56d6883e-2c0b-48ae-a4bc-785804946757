import 'package:the_voice_directory_flutter/core/api/api_params.dart';

class RouteName {
  static const splash = "Splash";
  static const signUp = "Sign Up";
  static const verifyOtp = "Verify Otp";
  static const login = "Login";
  static const enterInformation = "Enter information";
  static const createProfile = "Create Profile";
  static const projectType = 'Project Type';
  static const vocalCharacteristics = 'Vocal characteristics';
  static const uploadSamples = 'Spload Samples';
  static const dashboard = 'Dashboard';
  static const postJob = 'Post Job';
  static const profile = 'Profile';
  static const editBasicDetails = 'Edit Basic Details';
  static const editProfileDetails = 'Edit Profile Details';
  static const editPreferredProjectType = 'Edit Preferred Project Type';
  static const editAudio = 'Edit Audio Samples';
  static const editVocalCharacteristics = 'Edit Vocal Characteristics';
  static const jobDetail = 'Job Detail';
  static const myProfile = 'My Profile';
  static const applicationDetail = 'Application Detail';
  static const addNewJob = 'Add New Job';
  static const editJobRequirement = 'Edit Job Requirement';
  static const editJobTimeline = 'Edit Job Timeline';
  static const editJobBudget = 'Edit Job Budget';
  static const applyJob = 'Apply Job';
  static const jobReview = 'Job Review';
  static const jobPayment = 'Job Payment';
  static const notification = 'Notification';
  static const reviews = 'Reviews';
  static const googleCalender = 'Google Calender';
  static const chat = 'Chat';
  static const landingPage = 'Landing Page';
  static const voiceProfile = 'Voice Profile';
  static const explorePublicVoices = 'Explore Public Voices';
}

class RoutePath {
  static const splash = "/splash";
  static const signUp = "/signup";
  static const verifyOtp =
      "/verify_otp/:${Params.email}/:${Params.phoneNumber}";
  static const login = "/login";
  static const enterInformation = "/enter_information/:${Params.email}";
  static const createProfile = "/create_profile/:${Params.type}";
  static const vocalCharacteristics = '/vocal_characteristics';
  static const projectType = '/project_type';
  static const uploadSamples = '/upload_samples';
  static const dashboard = '/dashboard';
  static const postJob = '/post_job';
  static const profile = '/profile/:${Params.id}'; // Adding ? makes the parameter optional
  static const editBasicDetails = '/edit_basic_details';
  static const editProfileDetails = '/edit_profile_details';
  static const editPreferredProjectType = '/edit_preferred_project_type';
  static const jobDetail = '/job_detail/:${Params.id}/:${Params.showApplicantTab}';
  static const editAudio = '/edit_audio_samples';
  static const editVocalCharacteristics = '/edit_vocal_characteristics';
  static const myProfile = '/my_profile';
  static const applicationDetail = '/application_detail/:${Params.id}';
  static const editJobRequirement = '/edit_job_requirement';
  static const editJobTimeline = '/edit_job_timeline';
  static const editJobBudget = '/edit_job_budget';
  static const applyJob = '/apply_job/:${Params.id}';
  static const jobReview = '/job_review/:${Params.id}/:${Params.name}';
  static const jobPayment = '/job_payment/:${Params.id}';
  static const notification = '/notification/:${Params.isPrivate}';
  static const reviews = '/reviews/:${Params.id}';
  static const googleCalender = '/google_calendar/:${Params.id}';  // Define with parameter
  static const chat = '/chat/:${Params.needBackBtn}/:${Params.jobId}/:${Params.jobName}/:${Params.groupId}/:${Params.idTo}/:${Params.idFrom}/:${Params.myName}/:${Params.myProfileImg}/:${Params.name}/:${Params.profileImg}';
  static const landingPage = '/';
  static const voiceProfile = '/voice/:${Params.id}';
  static const explorePublicVoices = '/explore_public_voices';
}