class AppStrings {
  static const theVoiceDirectory = "The Voice Directory";
  static const phoneNumber = "Phone number*";
  static const enterPhoneNumber = "Enter phone number";
  static const verifyOTP = "Verify with OTP";
  static pleaseEnter6DigitOtp(String type) =>
      "Please enter the 6-digit OTP sent to your $type.";

  static const resendCode = "Resend Code";
  static const continueBtnText = "Continue";
  static const email = "Email*";
  static const enterEmail = "Enter email";
  static const uploadAudio = "Upload audio";
  static const uploadAudioFromFile = "Upload audio from files (upto 10)";
  static browseFileToUploadFileType({int maxSize = 50}) =>
      "Browse file to upload. File type - mp3 or .wav (max. $maxSize MB)";
  static const browseFile = "Browse file";
  static const continues = "Continue";
  static const fistName = "First name*";
  static const enterFirstName = "Enter first name";
  static const lastName = "Last name*";
  static const enterLastName = "Enter last name";
  static const enterYourEmail = "Enter your email";
  static const selectRole = "Select role*";
  static const client = "Client";
  static const voice = "Voice";
  static const ancillary = "Ancillary";
  static const ancillaryServices = "Ancillary Service";
  static const admin = "Admin";
  static const unknown = "Unknown";
  static const enterEnformation = "Enter information";
  static const pleaseFillBelowInformationToCreateProfile =
      "Please fill below information to create your profile";
  static const enterInformation = "Enter information";
  static const pleaseFillBelowInformationToCreateYourProfile =
      "Please fill below information to create your profile";
  static const login = "Login";
  static const loginToContinue = "Login to continue to your account ";
  static const continueTxt = "Continue";
  static const back = "Back";
  static const next = "Next";
  static const continueWithGoogle = "Continue with Google";
  static const continueWithApple = "Continue with Apple";
  static const signUpWithGoogle = "Sign up with Google";
  static const signUpWithApple = "Sign up with Apple";
  static const dontHaveAcc = "Don’t have an account?";
  static const signUp = "Sign up";
  static const youCanUploadMax = 'You can upload a maximum of 5 files.';
  static const browseImage = 'Browse image';
  static const preferredProjectType = 'Preferred project type';
  static const projectType = 'Project type*';
  static const toFindJobsThatAlignWithYourPreferences = 'To find jobs that align with your preferences, ';
  static const chooseTheTypeOfProjectYouWantToWorkOn = 'choose the type of project you want to work on.';
  static const selectProjectType = 'Select project type';
  static const select = 'Select';
  static const createAccountToContinue =
      "Create your account to continue to sign up";
  static const alreadyHaveAnAccount = "Already have an account?";
  static const signUpMarketing =
      "By continuing, you consent to receiving marketing news, offers, and promotional material from The Voice Directory";

  //Client Profile Screen
  static const createProfile = "Create profile";
  static const addYourDetails = "Add your details below to finish your profile";
  static const bio = "Bio*";
  static const tellUsALittleAboutYourself = "Tell us a little about yourself";
  static const selectGender = "Select gender";
  static const selectServices = "Select your services";
  static const gender = "Gender*";
  static const services = 'Services*';
  static const addCompanyName = "Add Company name";
  static const company = "Company*";
  static const selectIndustry = "Select industry";
  static const industry = "Industry*";
  static const bySigningUp = 'By signing up you agree to our ';
  static const termsAndConditions = "Terms & conditions ";
  static const and = "and ";
  static const privacyPolicy = 'privacy policy';
  static const permissionDenied = 'Permission Denied';
  static const audioAccessDeniedPleaseGoToSettingsAndAllow =
      'Audio access denied, please go to settings and allow';
  static const permissionRequiredSelectAudioFiles =
      'Permission is required to select audio files.';
  static const youCanUploadMaximum10Files = 'You can upload a maximum of 10 files.';
  static const youCanUploadMaximum1Files = 'You can upload only 1 file.';
  static const goToSettings = 'Go to Settings';
  static const accessDenied = 'Access denied !';
  static const dismiss = 'Dismiss !';
  static const vocalCharacteristics = 'Vocal characteristics';
  static const voiceGender = 'Voice gender*';
  static const voiceType = 'Voice type*';
  static const selectVoiceType = 'Select voice type';
  static const selectVoiceGender = 'Select voice gender';
  static const voiceCharacter = 'Voice character*';
  static const scriptLanguage = 'Script language';
  static const selectVoiceCharacter = 'Select voice character';
  static const voiceAge = 'Voice age*';
  static const selectVoiceAge = 'Select voice age';
  static const language = 'Language*';
  static const selectLanguage = 'Select language';
  static const nativeLanguage = 'Native language*';
  static const accent = 'Accent*';
  static const selectAccent = 'Select accent';
  static const otpSentOnEmail = 'OTP sent on your email';
  static const otpSentOnPhone = 'OTP sent on your phone number';
  static const failedToUploadAudio = 'Failed to upload audio, please retry!';
  static const genericErrorMsg = "Oops! Something went wrong.";
  static const done = 'Done';
  static const takeAPicture = 'Take a picture';
  static const uploadNewProfile = 'Upload new profile';
  static const noImageSelected = 'No image selected';
  static const cancel = 'Cancel';
  static const permissionDeniedPleaseGoToSettingsAndAllow =
      'Permission denied, please go to settings and allow';
  static const somethingWentWrong = 'Somthing went wrong';
  static const somethingWentWrongPleaseTryAgain =
      'Somthing went wrong. Please try again.';
  static const refresh = 'refresh';
  static welcomeToTVD(String userName) =>
      'Hi $userName,\nWelcome to The Voice Directory';
  static const vocalCharSubmitSuccess =
      'Vocal characteristics submited successfully!';
  static const audioSampleSuccess = 'Audio samples submited successfully!';
  static const projectTypeSuccess = 'Project type submited successfully!';
  static const welcomeToTVDDes =
      'Congratulations! You’ve successfully created your account. Click "Continue" to explore artists and post jobs';
  static const welcomeToTVDDesVoice =
      'Congratulations! You’ve successfully created your account. Click "Continue" to explore jobs, and get hired.';
  static const streetAddress = "Street address*";
  static const enterAddress = "Enter your address";
  static const city = "City*";
  static const enterCity = "Enter city name";
  static const state = "State/ Province*";
  static const enterState = "Enter state name";
  static const postalCode = "Postal code*";
  static const enterPCode = "Enter postal code";
  static const country = "Country*";
  static const enterCountry = "Enter country";
  static const tags = "Tags (upto 10)";
  // static const enterTags = "Enter tags";
  //Logout
  static const logout = 'Logout';
  static const areYouSureYouWantToLogOut = 'Are you sure you want to log out?';
  static const yesLogout = 'Yes, logout';
  //Block
  static const blockUser = 'Block User?';
  static const unblockUser = 'Unblock User?';
  static const areYouSureYouWantToBlock = 'Are you sure you want to block this user?';
  static const areYouSureYouWantToUnblock = 'Are you sure you want to unblock this user?';
  static const yesBlock = 'Yes, block';
  static const yesUnblock = 'Yes, unblock';

  // Dashboard Bottom Nav bar
  static const jobs = 'Jobs';
  static const calendar = 'Calendar';
  static const chats = 'Chats';
  static const account = 'Account';

  // Post a job
  static const title = 'Title*';
  static const enterProjectTitle = 'Enter project’s title';
  static const addRequirement = 'Add requirement';
  static const category = 'Category*';
  static const selectCategory = 'Select category';
  static const addKeywords = 'Add keywords';
  static const upTo10 = ' (up to 10)';
  static const addKeywordsRequirement =
      'Add keywords to narrow down requirement';
  static const postAJob = 'Post a Job';
  static const editJob = 'Edit job';
  static const pasteScript = 'Paste a script or URL as sample';
  static const uploadFile = 'Upload audio file';
  static const noSample = 'No sample script';
  static const pasteScriptHint = 'Type or paste script or URL';
  static const pasteUrl = 'Paste your script or URL*';
  static const uploadScriptSample = 'Upload script sample*';
  static const postJob = 'Post a job';
  static const budgetType = 'Budget type*';
  static const range = 'Range';
  static const fixed = 'Fixed';
  static const rangePrice = 'Range*';
  static const budgetPrice = 'Budget*';
  static const priceInINR = ' (prices are in INR)';
  static const enterAmount = 'Enter Amount';
  static const min = 'Min';
  static const max = 'Max';
  static const remote = 'Remote';
  static const inStudio = 'In Studio';
  static const selectLocation = 'Select location*';
  static const studioName = 'Studio name*';
  static const studioLocation = 'Studio location';
  static const enterStudioName = 'Enter studio name';
  static const addAddress = 'Add address*';
  static const addressLine1 = 'Address line 1*';
  static const addressLine2 = 'Address line 2';
  static const addressLine1Enter = 'Enter address line 1';
  static const addressLine2Enter = 'Enter address line 2';
  static const whichOptionDescribes =
      'Which option best describes the voice you are looking for?*';
  static const specifyOther = 'Specify “other”*';
  static const specifyOtherEnter =
      'Enter what voice character you are looking for';
  // static const tentativeDuration = 'Tentative dubbing duration*';
  static const inMinutes = ' (in minutes)';
  // static const tentativeDurationEnter = 'Enter tentative dubbing duration';
  static const enterHours = 'Enter hours';
  static const enterMinDuration = 'Enter minutes';
  static const enterSecDuration = 'Enter seconds';
  static const duration = 'Duration of Content*';
  static const projectDeadline = 'Project deadline*';
  static const projectDeadlineEnter = 'Enter project deadline';
  static const responseDeadlineAsterisk = 'Response deadline*';
  static const responseDeadlineEnter = 'Enter response deadline';
  static const gstText =
      'Final budget shown to the artist will include a 12% GST on the amount.';
  static const urlSampleScript = 'URL Sample Script';
  static const fileSampleScript = 'File Sample Script';
  static const noScriptSample = 'No Script Sample';
  static const private = 'Private';
  static const public = 'Public';

  // Post a JOb - Select invitees screen
  static const selectInvitees = 'Select invitees*';

  //Post a JOb - Select invitees screen
  static const post = 'Post';
  static const areYouSureYouWantToPostPublicly =
      'Are you sure you want to post this job publicly?';
  static const areYouSureYouWantToAcceptThisApplication = 'Are you sure you want to accept this application?';
  static const onceYouAcceptThisApplication= 'Once you Accept this application, all other applied voices will be notified that this job has been fulfilled';
  static const areYouSureYouWantCancelJob = 'Are you sure you want to cancel?';
  static const theJobWillBeVisibleToEveryVoice =
      'The job will be visible to every voice on the platform';
  static const areYouSureYouWantCancelJobDes =
      'If you cancel this job post, all progress will be lost.';
  static const yes = 'Yes';
  static const inviteVoices = 'Invite voices';
  static const searchAnArtist = 'Search an artist by name,\nemail or contact number';
  static const search = 'Search';
  static const searchHintText = 'Search Jobs by Name or Keywords';
  static const okay = 'Okay';
  static const myProfile = 'My profile';
  static const profileInformation = 'PROFILE INFORMATION';
  static const bio_ = 'Bio';
  static const gender_ = 'Gender';
  static const location = 'Location';
  static const edit = 'Edit';
  static const details = 'DETAILS';
  static const vocalCharacterstics = 'VOCAL CHARACTERISTICS';
  static const projectPreference = 'PROJECT PREFERENCE';
  static const vocalSamples = 'VOCAL SAMPLES';
  static const editInformation = 'Edit information';
  static const editVocalCharacteristics = 'Edit vocal characteristics';
  static const editUploadAudio = 'Edit audio samples';
  static const editPreferredProjectType = 'Edit preferred project type';
  static const keywords = 'Keywords';
  static const address = 'Address';
  static const editProfile = 'Edit profile';
  static const profile = 'Profile';
  static const notifications = "Notifications";
  static const others = "Others";
  static const contactUs = "Contact us";
  static const deleteAccount = "Delete account";
  static const manage = "Manage";
  static const company_ = "Company";
  static const industry_ = "Industry";
  static const enterYourLocation = 'Enter your location';
  static const enterYourStreet = 'Enter your street ';
  static const enterYourCity = 'Enter your city';
  static const enterYourState = 'Enter your state/Provice';
  static const enterPostalCode = 'Enter postal code';
  static const editImage = 'Edit image';
  static const save = 'Save';
  static const jobPostedSuccess = 'Job successfully posted';
  static const updated = 'Profile details updated';
  static const verifyMobileNumber = 'Please verify your mobile number!';
  static const noAudioSamplesAvailable = 'No audio samples available';
  static const retry = 'Retry';

  static const saveAndNext = 'Save & Next';
  static const enterPin = 'Enter pin';
  static const applicationDetails = 'Application Details';
  static const proposal = 'PROPOSAL';
  static const yourRevisionPolicy = 'YOUR REVISION POLICY';
  static const responseFile = 'RESPONSE FILE';
  static const quote = 'QUOTE';
  static const totalPrice = 'Total Price';
  static const rate = 'Rate';
  static const platformFee = 'Platform fee';
  static const dateOfApplication = 'Date of application';
  static const delete = 'Delete';
  static const requirement = 'REQUIREMENT';
  static const invitation = 'Invitation';
  static const type = 'Type';
  static const highlights = 'HIGHLIGHTS';
  // static const estimatedDuration = 'Estimated duration';
  static const timeLine = 'TIMELINE';
  static const invitees = 'INVITEES';
  static const clientDetails = 'Client details';
  static const voiceDetails = 'Voice details';
  static const overview = 'OVERVIEW';
  static const sampleScript = 'Sample script/ URL/ uploaded file';
  static const apply = 'Apply';
  static const datePosted = 'Date posted';
  static const jobRequirement = 'Job requirement';
  static const viewApplication = 'View application';
  static const markComplete = 'Mark complete';
  static const completed = 'Completed';
  static const noAudio = 'No sample script available';
  static const accountDetails = 'Account details';
  static const verify = 'Verify';
  static const noChangesMade =
      'No modifications were made, please make the necessary changes before saving';
  static const billingHistory = 'Billing History';
  static const reviews = 'Reviews';
  static const jobDetails = 'Job details';
  static const all = 'All';
  static const shortlisted = 'Shortlisted';
  static const applied = 'Applied';
  static const applicants = 'Applicants';
  static const noApplicantsYet = 'No applicants yet!';
  static const plsSelectAudioFile = 'Please select at least one audio file';
  //Post a Job - Summary Page
  static const jobSummary = 'Job Summary';
  static const jobName = 'Job name';
  static const vocalRequirement = 'Vocal requirement';
  static const jobBudget = 'Job budget';
  static const sampleScriptUrlUploadedFile =
      'Sample script/ URL/ uploaded file';

  static const allJobs = 'All jobs';
  static const open = 'Open';
  static const deciding = 'Deciding';
  static const inProgress = 'In Progress';
  static const closed = 'Closed';
  static const newest = 'Newest';
  static const oldest = 'Oldest';
  static const highToLow = 'High to low';
  static const highToLowName = 'High to low (Budget)';
  static const lowToHigh = 'Low to high';
  static const lowToHighName = 'Low to high (Budget)';
  static const searchByTitle = 'Search by job name or ID';
  static const sortBy = 'SORT BY';
  static const responseDeadline = 'Response deadline';
  static const noJobsPostedYetClient = 'You have not posted any job yet !';
  static const noJobsPostedYetVoice =
      'No jobs yet! Check again after some time!';
  static const requirementAsterisk = 'Requirement*';
  static const jobEditedSuccessfully = 'Job edited successfully';
  static const hiring = 'Hiring';
  static const selected = 'Selected';
  static const history = 'History';
  static const favourites = 'Favourites';
  static const accepted = 'Accepted';
  static const areYouSureYouWantToDeleteThisJob =
      'Are you sure you want to delete this job?';
  static const yesDeleDeletingThisWillPermanently =
      'Deleting this will permanently delete it. You will not be able to retrieve it';
  static const jobDeletedSuccessfully = 'Job deleted successfully';
  static const applyToJob = 'Apply to job';
  static const yourProposal = 'Your proposal *';
  static const introduceYourSelf =
      'Introduce yourself and let clients know what to expect working with you.';
  static const yourQuote = 'Your revision policy';
  static const explainWhatYou =
      'Explain what you include free of charge and/or your rate for additional project changes.';
  static const yourQuoteForPorject = 'Your quote for the project * (in INR)';
  static const yourQuoteWillInclude =
      'Your quote will include 20% of the platform fee which The Voice Directory charges.';
  static const finalQuoteForClient = 'Final quote for client';
  static const toBeCalculated = 'To be calculated';
  static const theClientWillSeeThisAmount = 'The client will see this amount';
  static const uploadProjectSpecificFile = 'Upload project specific file*';
  static const hereYouCanUpload =
      'Here you can upload an Audio or Video that is required by Client in the application for this job';
  static const submit = 'Submit';
  static const jobAppliedSuccessfully = 'Job Applied Successfully';
  static const applicantDetails = 'Applicant details';
  static const shortlist = 'Shortlist';
  static const myApplication = 'My application';
  static const noJobsFound = 'No jobs found';
  static const candidateShortlistedSuccessfully =
      'Candidate shortlisted successfully.';
  static const experience = 'Experience*';
  static const selectExperience = 'Select experience';

  static const writeAReview = 'Write a review';
  static writeAReviewDescription(String name) =>
      "Write a review and add rating as per your experience working with $name";
  static const addYourRating = 'Add your rating*';
  static const writeYourReview = 'Write your review*';
  static const writeReviewHint = 'Write review';
  static const selectStarRating = 'Please select rating before submitting.';
  static const payment = 'Payment';
  static const paymentModeTitle =
      'Select mode of how you will like to receive payment';
  static const theSelectedPaymentMode =
      'The selected payment mode was offline. Please confirm amount';
  static const onlinePayment = 'Online payment';
  static const offlinePayment = 'Offline payment';
  static const areYouSureTitle = 'Are you sure?';
  static const clickToMarkJobComplete =
      'Click on done to mark this job as complete.';
  static const reviewSubmittedSuccessfully =
      'Review submitted! The job has been successfully marked as completed.';
  static const offline = 'Offline';
  static const jobCompleted = 'Job completed';
  static const yayy =
      'Yayy... Your payment was successful and the job was marked as complete';
  static const theVoiceNeedsToComplete  =
      'The Voice needs to complete the job first in order for you to complete';
  static const accept = 'Accept';
  static const candidateAcceptedSuccessfully =
      'Candidate accepted successfully.';
  static const exploreVoices = 'Explore voices';
  static const searchArtistsHintText = 'Search by name or keyword';
  static noArtistsFound({bool? isSearchLocationBased}) => 'Couldn’t find any user associated with this name or keyword${isSearchLocationBased == true ? ' or location' : ''} or filter.\nPlease check and enter again.';

  static const experienceWithoutAsterisk = 'Experience';
  static const accentWithoutAsterisk = 'Accent';
  static const languageWithoutAsterisk = 'Language';
  static const voiceAgeWithoutAsterisk = 'Voice age';
  static const servicesWithoutAsterisk = 'Services';
  static const filter = 'Filter';
  static const reset = 'Reset';
  static const jobCompletedMsgVoice = 'Job marked as completed! Awaiting client review.';
  static const jobCompletedMsgClient = "Job completed successfully! Moved to 'Closed'.";
  static const amountCannotBe0 = 'Amount cannot be 0.';
  static const candidateAlreadyAccepted = "You've already accepted another applicant for this job.";
  static const thisWillHelpClientsFindYou = "This will help clients find you";
  static const typeAndPressEnterToAddAKeyword = 'Type and press enter or comma to add a keyword.';
  static const audioVideo10MbLimit = 'Audio / Video (Limit up to 50 MB)';
  static const noNotificationsYet = 'No notifications yet!';
  static const notification = 'Notification';
  static const viewAll = 'View all';
  static const readLess = 'Read less';
  static const readMore = 'Read more';
  static const deletingThisWillPermanentlyDelete =
      'Deleting this will permanently delete the account. You will not be able to retreive it.';
  static const areYouSureYouWantToDeleteAccount = 'Are you sure you want to delete your account?';
  static const noReviewsYet = "There aren't any reviews yet.";
  static const String allVoices = 'All voices';
  static const String favoriteVoices = 'Favorite voices';
  static const String favorites = 'Favorite';
  static const String previouslyHiredVoices = 'Previously hired voices';
  static const String unableToOpenEmail = 'Unable to open email client';
  static const String bookVoice = 'Book Voice';
  static const String youCanScheduleSlot =
      'You can schedule a slot with voice.';
  static const String bookAslot = 'Book a slot';
  static const String editCalendarLink = 'Edit calendar link';
  static const String addCalendarLink = 'Add calendar link';
  static const update = 'Update';
  static const String connectWithYourCalendarToManage = 'Connect with your calendar to manage your schedule on The Voice Directory app';
  static const String enterCalendarLink = 'Enter calendar link';
  static const String noCalendarLink = 'No calendar link added yet. click “Add link” to add a your calendar link';
  static const String addLink = 'Add link';
  static const String createNewEvent = 'Create a new event';
  static const String selectDateAndTime = 'Select date and time*';
  static const String enterEventTitle = 'Please enter an event title';
  static const String selectStartTime ='Please select a start time';
  static const String selectEndTime ='Please select an end time';
  static const String sameTime = 'Start and end time cannot be the same';
  static const String endTimeAfterStartTime ='End time must be after start time';
  static const String connectWithCalendar = 'Connect with calendar';
  static const String addTitle = 'Add title*';
  static const String meetLink = 'Meet link';
  static const String eventLocation = 'Event location*';
  static const String enterEventLocation = 'Enter event location';
  static const String enterMeetLink = 'Enter meet link';
  static const String allEvents = 'All Events';
  static const String disconnectCalendar = 'Disconnect calendar?';
  static const String areYouSureYouWantToDisconnectCalendar = 'Are you sure you want to disconnect calendar from Google calendar?';
  static const String yesDisconnect = 'Yes, disconnect';
  static const String calendarDisconnectedSuccessfully = 'Calendar disconnected successfully';
  static const String eventCreatedSuccessfully = 'Event created successfully';
  static const String youHaveNotAddedFavorite = 'You haven’t added any jobs to favourites yet! Tap the heart icon on any job to save it here for quick access!';
  static const String chat = 'Chat';
  static const String noChatAvailable = 'No chat available';
  static const String tapOnChatToSeeConversation = 'Tap on a chat to see the conversation';
  static const String seconds = 'Seconds';
  static const String minutes = 'Minutes';
  static const String hours = 'Hours';
  static const String sayHiToStartConversation = 'Say "Hi" to start the conversation.';
  static const String typeYourMessageHere = 'Type your message here';
  static const String theUserHasNotAdded = 'The user has not added a calendar yet';
  static const String viewDetail = 'View detail';
  static const String fileDownloadedSuccessfully = 'File downloaded successfully';
  static const String fileDownloadFailed = 'File download failed';
  static const report = 'Report';
  static const reportUser = 'Report User';
  static const areYouSureToReport ="Are you sure you want to report this user?";
  static const pleaseEnterReasonForReporting = "Please enter why you’re reporting this user";
  static const explore = 'Explore';
  static const price = 'Price';
  static const areYouSureYouWantSubmitReview = 'Are you sure you want submit review and mark this job as completed?';
  static const complete = 'Complete';
  static const failedToUpdateInvitees = 'Failed to update invitees';
  static const privateInvite = 'Private invite';
  static const inviteSentSuccessfully = 'Invite sent successfully';
  static const about = 'About';
  static const howItWorks = 'How it works';
  static const oneStopPlatformToFind = 'One-stop platform to find';
  static const theBestProfessional = 'THE BEST PROFESSIONAL';
  static const voiceTalent = 'VOICE TALENT';
  static const findVoiceTalentDescription =
      "Discover, hire, or showcase top-tier voice talent–all in one place. TVD connects Voice Actors with opportunities across every genre and industry.";
  static const findVoice = 'Find Voice';
  static const findWork = 'Find Work';
  static const needVoiceTitle = 'Need a Voice? We’ve Got the Perfect Match';
  static const hireOrGetHiredTitle = 'Hire the Voice You Need or Be the One They Need';
  static const hireOrGetHiredDescription = 'TVD is your all-in-one voice talent platform, built to empower Creators, Artists, and Studios alike.';
  static const hireOrGetHiredItem1 = 'Diverse Voice Categories';
  static const hireOrGetHiredItem2 = 'Seamless Hiring Process';
  static const hireOrGetHiredItem3 = 'Verified Talent & Clients';
  static const hireOrGetHiredItem4 = 'Save Time & Money';
  static const learnMore = 'Learn more';
  static const enrollToOurPlatformTitle = 'Start Your Voice Journey Today!';
  static const enrollToOurPlatformDescription = "Scan the QR code to sign up instantly. Whether you're here to hire talent or showcase yours. Join TVD and be part of a growing Voice Over community.";
  static const scanToJoinInstantly = ' Scan to join instantly!';
  static const joinNow = 'Join Now';
  static const howThePlatformWorks = 'How to Become a Part of TVD?';
  static const howThePlatformWorksDescription = 'Join a growing ecosystem of voices, creators, and service providers, designed to connect talent with opportunities.';

  static const String voiceItem1Title = 'Sign Up & Build Your Profile';
  static const String voiceItem1Description = 'Create a professional portfolio with your demos, expertise, and specialization.';
  static const String voiceItem2Title = 'Get Verified';
  static const String voiceItem2Description = 'Complete the quick verification process to build trust and gain visibility.';
  static const String voiceItem3Title = 'Showcase & Apply';
  static const String voiceItem3Description = 'Browse available projects or let your profile speak for itself.';
  static const String voiceItem4Title = 'Get Hired & Paid';
  static const String voiceItem4Description = 'Accept gigs, deliver your best work, and get paid seamlessly.';
  
  static const String clientItem1Title = 'Create an Account';
  static const String clientItem1Description = 'Set up your profile and define your voice talent or service needs.';
  static const String clientItem2Title = 'Post a Job';
  static const String clientItem2Description = 'Share project details to attract relevant voice actors or service providers.';
  static const String clientItem3Title = 'Review & Select';
  static const String clientItem3Description = 'Listen to samples, compare profiles, and choose the right fit.';
  static const String clientItem4Title = 'Collaborate & Complete';
  static const String clientItem4Description = 'Manage the workflow easily and receive high-quality recordings.';

  static const String ancillaryItem1Title = 'Register as a Service Partner';
  static const String ancillaryItem1Description = 'Tell us what you do, writing, recording or supervision, etc.';
  static const String ancillaryItem2Title = 'Add Your Service Listings';
  static const String ancillaryItem2Description = 'Showcase your offerings with sample work, pricing, and turnaround time.';
  static const String ancillaryItem3Title = 'Connect with Talent or Clients';
  static const String ancillaryItem3Description = 'Get discovered by voice actors or clients who need your support.';
  static const String ancillaryItem4Title = 'Deliver & Build Reputation';
  static const String ancillaryItem4Description = 'Complete orders, collect ratings, and grow with every project.';

  static const String getStarted = 'Get Started';
  static const String whatOurUsersSay = 'What Our Users Say About TVD';
  static const String checkOutSomeTestimonials = 'From voice actors to clients and service partners, hear how TVD has helped them connect, collaborate, and create success stories.';
  static const String contactUsTitle = 'Join Our One-Stop\nVoice Talent Today!';
  static const String contactUsDescription = 'Talent. Opportunity. Growth. It all starts here. Fill out the form and take your first step with TVD.';

  static const String supportEmail = '<EMAIL>';
  static const String supportPhone = '+91 022-3512-2188/89/90';
  static const String supportAddress = 'Sangam C Wing, 2nd Floor, Above State Bank of India, Juhu Versova Link Road, Andheri West, Mumbai 400 053.';
  static const String supportAddressUrl = 'https://maps.app.goo.gl/6FzCTuHa1qigA6W68';

  static const String name = 'Name';
  static const String message = 'Message';
  static const String enterYourName = 'Enter your name';
  static const String enterYourTitle = 'Enter your enquiry title';
  static const String enterYourMessage = 'Enter your enquiry message';
  static const String send = 'Send';
  static const String copyright = '© 2025 Sound & Vision India Private Limited';
  static const String allRightsReserved = 'All rights reserved';
  // static const String copyright = '© 2025 Sound & Vision India Private Limited. All rights reserved';
  static const String termsAndConditionsFooter = 'Terms and Conditions';
  static const String privacyPolicyFooter = 'Privacy Policy';
  static const String seeMoreArtists = 'See More Artists';
  static const String topArtists = 'Top Artists';
  static noSearchResultFoundArtist(String searchQuery) => 'No search result found with “$searchQuery” Please check and search again.';
  static const String toPersonalizeYourExperience = ' to personalize your experience!';
  static hello(String name) => 'Hello, $name';

  static const String messageSentSuccessfully = 'Message sent successfully';
  static const String continueAsGuest = 'Continue as Guest';
  static const noUserFound = 'No user found';
  static const String exploreVoicesTitle = 'Explore the Right Voice for Every Medium';
  static const String exploreVoicesDescription = 'From smooth narrations to energetic promos, we have the voice that fits your narrative perfectly.';
  static const String logIn = 'Log In';
  static const String emailAddress = 'Email Address';
  static const String enterLocation = 'Enter location';
  static const String showFavouritesOnly = 'Show favourites only';
}

class ValidationMsg {
  static const enterFirstName = 'Please enter first name';
  static const enterLastName = 'Please enter last name';
  static const enterEmail = 'Please enter email';
  static const enterValidEmail = 'Please enter valid email';
  static const enterPhoneNumber = 'Please enter a valid phone number';
  static const audioSizeError = 'Audio size should be less then 50 MB';
  static const plsEnterOtp = 'Please enter OTP';
  static const plsEnterValidOtp = 'Please enter valid OTP';
  static const plsEnterBio = 'Please enter bio';
  static const selectRole = 'Please select a role';
  static const profilePic = 'Please upload profile picture';
  static const plsEnterCompanyName = 'Please enter company name';
  static const plsSelectIndustry = 'Please select at least one industry';
  static const plsSelectServices = 'Please select at least one service';
  static const plsSelectGender = 'Please select your gender';
  static const plsSelectVoiceGender = 'Please select voice gender';
  static const plsSelectVoiceType = 'Please select voice type';
  static const plsSelectVoiceChar = 'Please select voice characteristics';
  static const plsSelectVoiceAge = 'Please select voice age';
  static const plsSelectVoiceLang = 'Please select at least 1 language';
  static const plsSelectVoiceAccent = 'Please select at least 1 accent';
  static const plsSelectProjectType = 'Please select at least 1 project type';
  static const String plsSelectAudio = "Please select an audio file";
  static const plsAgreeToTnC =
      'Please agree to the Terms and Conditions and Privacy Policy to continue';
  static const plsEntervalidCompanyName =
      'Company name should be alphanumeric. Please enter valid company name';
  static const plsEntervalidAddress = 'Please enter valid address';
  static const plsEntervalidCity = 'Please enter valid city';
  static const plsEntervalidState = 'Please enter valid state/province';
  static const plsEntervalidPCode = 'Please enter valid postal code';
  static const plsEntervalidCountry = 'Please enter valid country';
  static plsEntervalid(String type) => 'Please enter valid $type';
  static plsEnter(String type) => 'Please enter $type';
  static plsSelect(String type) => 'Please select $type';
  static const plsSelectUser = 'Please select at least 1 user to invite';
  static const responseBeforeProjectDeadline =
      'Response deadline should be before project deadline';
  static const projectDeadlineAfterResponse =
      'Project deadline should be after response deadline';
  static const plsSelectCity = 'Please enter your city';
  static const plsSelectState = 'Please enter your state';
  static const plsSelectStreet = 'Please enter your street';
  static const plsSelectLocation = 'Please enter your location';
  static const plsSelectPostlCode = 'Please enter your postal code';
  static const plsSelectYourProposal = 'Please enter your proposal';
  static const plsEnterProjectAmount = 'Please enter project amount';
  static const plsUploadScriptSample = 'Please upload script sample';
}
class SecretKeys {
  static const oneSignalAppId = "************************************";
  static const String googleMapApiKey = "AIzaSyC898Rotr0ipt9o_ataxyN8UP7ubz7pPGE";
}

class EnumStrings {
  static const String about = 'about';
  static const String howItWorks = 'how_it_works';
  static const String exploreVoices = 'explore_voices';
  static const String faq = 'faq';
  static const String contactUs = 'contact_us';
  static const String testimonials = 'testimonials';
  static const String hireOrGetHired = 'hire_or_get_hired';
}
