import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:go_router/go_router.dart';
import 'package:table_calendar/table_calendar.dart';
import 'package:intl/intl.dart';
import 'package:the_voice_directory_flutter/core/services/hive/hive_keys.dart';
import 'package:the_voice_directory_flutter/core/services/hive/hive_storage_helper.dart';
import 'package:the_voice_directory_flutter/features/common/user_data/data/user_data_model.dart';
import 'package:the_voice_directory_flutter/features/google_calender/bloc/book_event_bloc.dart';
import 'package:the_voice_directory_flutter/features/google_calender/bloc/book_event_state.dart';
import 'package:the_voice_directory_flutter/features/google_calender/bloc/disconnect_calender_bloc.dart';
import 'package:the_voice_directory_flutter/features/google_calender/bloc/disconnect_calender_state.dart';
import 'package:the_voice_directory_flutter/features/google_calender/bloc/get_calender_event_bloc.dart';
import 'package:the_voice_directory_flutter/features/google_calender/bloc/get_calender_event_state.dart';
import 'package:the_voice_directory_flutter/features/google_calender/bloc/get_server_auth_code_bloc.dart';
import 'package:the_voice_directory_flutter/features/google_calender/bloc/get_server_auth_code_state.dart';
import 'package:the_voice_directory_flutter/features/google_calender/data/request_model.dart';
import 'package:the_voice_directory_flutter/features/google_calender/widgets/add_event_dialog.dart';
import 'package:the_voice_directory_flutter/features/google_calender/widgets/all_events_dialog.dart';
import 'package:the_voice_directory_flutter/features/google_calender/widgets/calendar_view_dropdown.dart';
import 'package:the_voice_directory_flutter/features/google_calender/widgets/custom_calendar.dart';
import 'package:the_voice_directory_flutter/features/voice/profile/presentation/error_screen.dart';
import 'package:the_voice_directory_flutter/utils/custom_toast.dart';
import 'package:the_voice_directory_flutter/utils/extensions/empty_space_extn.dart';
import 'package:the_voice_directory_flutter/utils/responsive.dart';
import 'package:the_voice_directory_flutter/utils/string_constants/app_images.dart';
import 'package:the_voice_directory_flutter/utils/string_constants/app_strings.dart';
import 'package:the_voice_directory_flutter/widgets/buttons/primary_button.dart';
import 'package:the_voice_directory_flutter/widgets/common_circle_icon.dart';
import 'package:the_voice_directory_flutter/widgets/dialogs.dart';
import 'package:the_voice_directory_flutter/widgets/loading_dialog.dart';
import 'package:the_voice_directory_flutter/widgets/texts/app_text.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:the_voice_directory_flutter/widgets/toggle_switch.dart';

import '../../common/social_sign_in/web_google_sign_in.dart';

class GoogleCalenderScreen extends StatefulWidget {
  final List<RequestModel> events;
  final String accessToken;
  final int? userId;

  const GoogleCalenderScreen({
    super.key,
    required this.events,
    required this.accessToken,
    this.userId,
  });

  @override
  State<GoogleCalenderScreen> createState() => _GoogleCalenderScreenState();
}

class _GoogleCalenderScreenState extends State<GoogleCalenderScreen> {
  late DateTime _focusedDay;
  DateTime? _selectedDay;
  Map<DateTime, List<RequestModel>> _eventMap = {};
  CalendarFormat _calendarFormat = CalendarFormat.month;
  UserDataModel? _userDataModel;
  String? _userId;
  bool _isConnected = true;

  @override
  void initState() {
    super.initState();
    _initializeUserData();
    _fetchInitialEvents();
  }

  void _initializeUserData() {
    _userDataModel = HiveStorageHelper.getData<UserDataModel>(
        HiveBoxName.user, HiveKeys.userData);
    _userId = (_userDataModel?.role == UserType.client || _userDataModel?.role == UserType.ancillaryService)
        ? widget.userId?.toString()
        : _userDataModel?.id?.toString();
    _isConnected = _userDataModel?.serverAuthCode ?? false;
    _focusedDay = DateTime.now();
    _selectedDay = _focusedDay;
  }

  void _fetchInitialEvents() {
    final now = DateTime.now();
    final firstDay = DateTime.utc(now.year, now.month, 1);
    final lastDay = DateTime.utc(now.year, now.month + 1, 0, 23, 59, 59);

    Future.microtask(() {
      if (mounted) {
        context.read<GetCalenderEventBloc>().getCalenderEvents(
              _userId.toString(),
              firstDay.toIso8601String(),
              lastDay.toIso8601String(),
            );
      }
    });
  }

  Map<DateTime, List<RequestModel>> _groupEvents(List<RequestModel> events) {
    final eventMap = <DateTime, List<RequestModel>>{};
    for (var event in events) {
      final dateKey =
          DateTime(event.start.year, event.start.month, event.start.day);
      eventMap.putIfAbsent(dateKey, () => []).add(event);
    }
    return eventMap;
  }

  List<RequestModel> _getEventsForDay(DateTime day) {
    final key = DateTime(day.year, day.month, day.day);
    return _eventMap[key] ?? [];
  }

  void _onDaySelected(DateTime selectedDay, DateTime focusedDay) {
    setState(() {
      _selectedDay = selectedDay;
      _focusedDay = focusedDay;
    });
  }

  void _onMonthChanged(DateTime newMonth) {
    setState(() => _focusedDay = newMonth);
    final firstDay = DateTime.utc(newMonth.year, newMonth.month, 1);
    final lastDay =
        DateTime.utc(newMonth.year, newMonth.month + 1, 0, 23, 59, 59);

    context.read<GetCalenderEventBloc>().getCalenderEvents(
          _userId.toString(),
          firstDay.toIso8601String(),
          lastDay.toIso8601String(),
        );
  }

  void _addEventDialog(BuildContext context) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AddEventDialog(
        initialDate: _selectedDay ?? DateTime.now(),
        onAddEvent: (title, start, end, emails, eventLocation) =>
            _handleAddEvent(TextEditingController(text: title), start, end,
                emails, TextEditingController(text: eventLocation)),
      ),
    );
  }

  Future<void> _handleAddEvent(
    TextEditingController titleController,
    DateTime start,
    DateTime end,
    List<String> attendees,
    TextEditingController eventLocationController,
  ) async {
    if (titleController.text.isEmpty) return;
    try {
      context.read<BookEventBloc>().bookCalenderEventApi(
            requestModel: RequestModel(
              userId: _userId.toString(),
              summary: titleController.text,
              start: start,
              end: end,
              attendees: attendees,
              eventLocation: eventLocationController.text,
            ),
          );
    } catch (e) {
      if (mounted) {
        Future.microtask(() => CustomToast.show(
            context: context, message: 'Failed to create event: $e'));
      }
    }
  }

  void _showAllEvents(List<RequestModel> events) {
    showDialog(
        context: context,
        builder: (context) => AllEventsDialog(events: events));
  }

  void _showDisconnectDialog() {
    Dialogs.showCommonDialog(
      context: context,
      title: AppStrings.disconnectCalendar,
      message: AppStrings.areYouSureYouWantToDisconnectCalendar,
      primaryButtonBgColor: const Color(0xFFD5E04D),
      primaryButtonText: AppStrings.yesDisconnect,
      primaryButtonColor: const Color(0xFF525252),
      onPrimaryButtonTap: () {
        context.read<DisconnectCalenderBloc>().disconnectCalendar();
        context.pop();
      },
    );
  }

  Widget _buildHeader() =>
      BlocBuilder<GetCalenderEventBloc, GetCalenderEventState>(
        builder: (context, state) {
          if (!Responsive.isDesktop(context)) {
            return Column(
              children: [
                Container(
                  color: Theme.of(context).colorScheme.primary,
                  child: Padding(
                    padding: EdgeInsets.only(
                        left: 16.w, right: 16.w, top: 60.h, bottom: 20.h),
                    child: Row(
                      children: [
                        TextDisplayLarge36And26(
                          AppStrings.calendar,
                        ),
                        const Spacer(),
                        if (_userDataModel?.role == UserType.voice &&
                            !Responsive.isDesktop(context) &&
                            state is GetCalenderSuccessState)
                          ToggleSwitch(
                            isOn: _isConnected,
                            onChanged: (value) {
                              if (!value) {
                                _showDisconnectDialog();
                              }
                            },
                          ),
                      ],
                    ),
                  ),
                ),
                20.ph,
                if (state is GetCalenderSuccessState) _buildHeaderControls(),
              ],
            );
          } else {
            return Row(children: [
              TextDisplayLarge36And26(AppStrings.calendar),
          const Spacer(),
              if (state is GetCalenderSuccessState) _buildHeaderControls(),
        ]);
          }
        },
      );

  Widget _buildHeaderControls() => Row(
        children: [
          _buildMonthNavigation(),
          if (!Responsive.isDesktop(context)) const Spacer() else 16.pw,
          CalendarViewDropdown(
            currentFormat: _calendarFormat,
            onFormatChanged: (format) =>
                setState(() => _calendarFormat = format),
          ),
          16.pw,
          if (_userDataModel?.role == UserType.voice &&
              !!Responsive.isDesktop(context))
            ToggleSwitch(
              isOn: _isConnected,
              onChanged: (value) {
                if (!value) {
                  _showDisconnectDialog();
                }
              },
            ),
        ],
      );

  Widget _buildMonthNavigation() => Row(
        children: [
          IconButton(
            icon: CommonCircleIcon(
                iconPath: AppImages.backwordIcon,
                containerSize: 32.h,
                iconSize: 32.h),
            onPressed: () => _onMonthChanged(
                DateTime(_focusedDay.year, _focusedDay.month - 1)),
          ),
          20.pw,
          TextTitle18And14(
            '${DateFormat('MMMM').format(_focusedDay)} ${_focusedDay.year}',
            style: const TextStyle(fontWeight: FontWeight.w500),
          ),
          20.pw,
          IconButton(
            icon: CommonCircleIcon(
              iconPath: AppImages.forwordIcon,
              containerSize: 32.h,
              iconSize: 32.h,
            ),
            onPressed: () => _onMonthChanged(
                DateTime(_focusedDay.year, _focusedDay.month + 1)),
          ),
        ],
      );

  void _handleGoogleSignIn() {
    if (kIsWeb) {
      WebGoogleSignIn.startGoogleSignInFlow((code, accessToken) {
        context
            .read<GetServerAuthCodeBloc>()
            .getServerAuthCode(serverAuthCode: code, deviceType: 'web');
      });
    } else {
      final GoogleSignIn googleSignIn = GoogleSignIn(
        scopes: [
          'email',
          'https://www.googleapis.com/auth/calendar',
        ],
        clientId: Platform.isIOS
            ? '************-dllemph2pc38bjornh76kl0aped8tl35.apps.googleusercontent.com'
            : null,
        serverClientId:
            '************-58uvtig2k4ld4313l7rcqng4hjuksin9.apps.googleusercontent.com',
        forceCodeForRefreshToken: true,
      );
      googleSignIn.signIn().then((account) async {
        if (account != null) {
          context.read<GetServerAuthCodeBloc>().getServerAuthCode(
              serverAuthCode: account.serverAuthCode, deviceType: 'Android');
        }
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final isClient = _userDataModel?.role == UserType.client;
    final isAncillary = _userDataModel?.role == UserType.ancillaryService;
    return Scaffold(
      body: MultiBlocListener(
        listeners: [
          BlocListener<GetServerAuthCodeBloc, GetServerAuthCodeState>(
            listener: (context, state) {
              if (state is GetServerAuthCodeLoadingState) {
                Dialogs.showOnlyLoader(context);
              } else {
                context.pop();
                if (state is GetServerAuthCodeSuccessState) {
                  setState(() {
                    _isConnected = true;
                    _userDataModel?.serverAuthCode = true;
                    HiveStorageHelper.saveData(
                        HiveBoxName.user, HiveKeys.userData, _userDataModel!);
                  });
                  final firstDay =
                      DateTime.utc(_focusedDay.year, _focusedDay.month, 1);
                  final lastDay = DateTime.utc(
                      _focusedDay.year, _focusedDay.month + 1, 0, 23, 59, 59);
                  context.read<GetCalenderEventBloc>().getCalenderEvents(
                        _userId.toString(),
                        firstDay.toIso8601String(),
                        lastDay.toIso8601String(),
                      );
                } else if (state is GetServerAuthCodeErrorState) {
                  setState(() {
                    _isConnected =
                        false; // Set connected state to false on error
                  });
                  CustomToast.show(context: context, message: state.errorMsg);
                }
              }
            },
          ),
          BlocListener<DisconnectCalenderBloc, DisconnectCalenderState>(
            listener: (context, state) {
              if (state is DisconnectCalenderLoadingState) {
                Dialogs.showOnlyLoader(context);
              } else {
                context.pop();
                if (state is DisconnectCalenderSuccessState) {
                  setState(() {
                    _isConnected = false;
                  });
                  final firstDay =
                      DateTime.utc(_focusedDay.year, _focusedDay.month, 1);
                  final lastDay = DateTime.utc(
                      _focusedDay.year, _focusedDay.month + 1, 0, 23, 59, 59);
                  context.read<GetCalenderEventBloc>().getCalenderEvents(
                        _userId.toString(),
                        firstDay.toIso8601String(),
                        lastDay.toIso8601String(),
                      );
                  CustomToast.show(
                    context: context,
                    message: AppStrings.calendarDisconnectedSuccessfully,
                    isSuccess: true,
                  );
                } else if (state is DisconnectCalenderErrorState) {
                  CustomToast.show(
                    context: context,
                    message: state.errorMsg,
                  );
                }
              }
            },
          ),
        ],
        child: Padding(
          padding: EdgeInsets.symmetric(
            vertical: !Responsive.isDesktop(context) ? 0.h : 40.h,
            horizontal: !Responsive.isDesktop(context) ? 0.h : 80.h,
          ),
          child: Column(
            children: [
              _buildHeader(),
              20.ph,
              BlocBuilder<GetCalenderEventBloc, GetCalenderEventState>(
                builder: (context, state) {
                  if (state is GetCalenderLoadingState) {
                    return Expanded(
                      child: Center(
                        child: Loader(),
                      ),
                    );
                  }
                  if (state is GetCalenderErrorState) {
                    return Padding(
                      padding: EdgeInsets.all(16.h),
                      child: _userDataModel?.role == UserType.voice
                          ? ErrorScreen(
                              buttonText: AppStrings.connectWithCalendar,
                              onRetry: () {
                                _handleGoogleSignIn();
                              },
                              errorMessage:
                                  AppStrings.connectWithYourCalendarToManage,
                              imageWidget: SvgPicture.asset(
                                AppImages.calendarEmptyIcon,
                                height: 200.h,
                                width: 100.w,
                              ),
                            )
                          : Center(
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  200.ph,
                                  const TextTitle18And14(
                                    AppStrings.theUserHasNotAdded,
                                    textAlign: TextAlign.center,
                                  ),
                                ],
                              ),
                            ),
                    );
                  }
                  if (state is GetCalenderSuccessState) {
                    final events = state.events
                        .map((e) => RequestModel(
                              userId: _userId.toString(),
                              summary: e.summary,
                              start: e.start,
                              end: e.end,
                              meetLink: e.meetLink,
                              attendees:
                                  e.attendees.map((e) => e.email).toList(),
                              eventLocation: e.location,
                            ))
                        .toList();
                    _eventMap = _groupEvents(events);
                    return CustomCalendar(
                      focusedDay: _focusedDay,
                      selectedDay: _selectedDay,
                      onDaySelected: _onDaySelected,
                      eventLoader: _getEventsForDay,
                      onShowAllEvents: _showAllEvents,
                      calendarFormat: _calendarFormat,
                    );
                  }
                  return const SizedBox.shrink();
                },
              ),
              if (isClient || isAncillary) ...[
                const Spacer(),
                BlocListener<BookEventBloc, BookEventState>(
                  listener: (context, state) {
                    if (state is BookEventLoadingState) {
                      Dialogs.showOnlyLoader(context);
                    }
                    if (state is BookEventSubmitSuccessState) {
                      context.pop();
                      CustomToast.show(
                          context: context,
                          message: AppStrings.eventCreatedSuccessfully,
                          isSuccess: true);
                      final firstDay =
                          DateTime.utc(_focusedDay.year, _focusedDay.month, 1);
                      final lastDay = DateTime.utc(_focusedDay.year,
                          _focusedDay.month + 1, 0, 23, 59, 59);
                      context.read<GetCalenderEventBloc>().getCalenderEvents(
                            _userId.toString(),
                            firstDay.toIso8601String(),
                            lastDay.toIso8601String(),
                          );
                    } else if (state is BookEventSubmitErrorState) {
                      CustomToast.show(
                          context: context, message: state.errorMsg);
                    }
                  },
                  child: PrimaryButton(
                    width: !Responsive.isDesktop(context)
                        ? null
                        : MediaQuery.of(context).size.width * .6,
                    onPressed: () => _addEventDialog(context),
                    buttonText: AppStrings.createNewEvent,
                  ),
                ),
              ]
            ],
          ),
        ),
      ),
    );
  }
}
