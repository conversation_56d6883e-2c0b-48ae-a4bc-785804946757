import 'dart:math';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:intl/intl.dart';
import 'package:table_calendar/table_calendar.dart';
import 'package:the_voice_directory_flutter/core/theme/app_theme.dart';
import 'package:the_voice_directory_flutter/features/google_calender/data/request_model.dart';
import 'package:the_voice_directory_flutter/utils/responsive.dart';
import 'package:the_voice_directory_flutter/widgets/texts/app_text.dart';

import '../../../utils/string_constants/app_strings.dart';
class CustomCalendar extends StatelessWidget {
  final DateTime focusedDay;
  final DateTime? selectedDay;
  final Function(DateTime selectedDay, DateTime focusedDay) onDaySelected;
  final List<RequestModel> Function(DateTime day) eventLoader;
  final Function(List<RequestModel>) onShowAllEvents;
  final CalendarFormat calendarFormat;
  
  const CustomCalendar({
    super.key,
    required this.focusedDay,
    required this.selectedDay,
    required this.onDaySelected,
    required this.eventLoader,
    required this.onShowAllEvents,
    required this.calendarFormat,
  });

  Widget _buildEventMarker(List<RequestModel> events, BuildContext context) {
    return Padding(
      padding: EdgeInsets.all(4.h),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          ...events.take(2).map((event) => Builder(
                builder: (context) => !Responsive.isDesktop(context)
                ? InkWell(
                    onTap: () => onShowAllEvents(events),
                    child: _buildEventMarkerItem(event),
                  )
                : _buildEventMarkerItem(event),
          )),
          if (!Responsive.isDesktop(context))
            InkWell(
              onTap: () => onShowAllEvents(events),
              child: Builder(
                builder: (context) => TextTitle14(
                  (events.length <= 2)
                      ? AppStrings.viewDetail
                      : '+${events.length - 2} more',
                  color: Theme.of(context).colorScheme.secondary,
                  fontSize: 12.sp,
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildEventMarkerItem(RequestModel event) {
    return Builder(
      builder: (context) => Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            width: 6.w,
            height: 6.h,
            margin: EdgeInsets.only(right: 4.w),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.green00843E,
              shape: BoxShape.circle,
            ),
          ),
          Expanded(
            child: TextTitle14(
              '${TimeOfDay.fromDateTime(event.start).format(context)} ${event.summary}',
              fontSize: 12.sp,
              overflow: TextOverflow.ellipsis,
              maxLines: 1,
            ),
          ),
        ],
      ),
    );
  }

  CalendarStyle get _calendarStyle => CalendarStyle(
    cellMargin: EdgeInsets.all(2.h),
    cellPadding: EdgeInsets.all(8.h),
    defaultDecoration: BoxDecoration(
      color: Colors.grey[50],
      border: Border.all(color: Colors.grey[200]!),
    ),
    weekendDecoration: BoxDecoration(
      color: Colors.grey[50],
      border: Border.all(color: Colors.grey[200]!),
    ),
    selectedDecoration: BoxDecoration(
      color: Colors.blue[100],
      border: Border.all(color: Colors.blue),
    ),
    todayDecoration: BoxDecoration(
      color: Colors.grey[50],
      border: Border.all(color: Colors.blue),
    ),
  );

  Widget _buildWeekViewContent(BuildContext context, DateTime day, List<RequestModel> events) {
    final hours = List.generate(24, (index) => index);
    
    return Column(
      children: [
        Align(
          alignment: Alignment.topCenter,
          child: Text(
            '${day.day}',
            style: const TextStyle(color: Colors.black),
          ),
        ),
        Expanded(
          child: ListView.builder(
            padding: EdgeInsets.only(top: 4.h),
            itemCount: hours.length,
            itemBuilder: (context, index) {
              final hour = hours[index];
              final timeSlotEvents = events.where((event) {
                final eventHour = event.start.hour;
                return eventHour == hour;
              }).toList();

              return SizedBox(
                height: 20.h,
                child: Row(
                  children: [
                    SizedBox(
                      width: 30.w,
                      child: Text(
                        '${hour.toString().padLeft(2, '0')}:00',
                        style: TextStyle(
                          fontSize: 8.sp,
                          color: Colors.grey[600],
                        ),
                      ),
                    ),
                    Container(
                      width: 0.5,
                      color: Colors.grey[300],
                    ),
                    if (timeSlotEvents.isNotEmpty)
                      Expanded(
                        child: ListView.builder(
                          scrollDirection: Axis.horizontal,
                          itemCount: timeSlotEvents.length,
                          itemBuilder: (context, index) {
                            final event = timeSlotEvents[index];
                            return Container(
                              margin: EdgeInsets.only(left: 4.w),
                              padding: EdgeInsets.symmetric(horizontal: 4.w),
                              decoration: BoxDecoration(
                                color: Colors.green.withOpacity(0.2),
                                borderRadius: BorderRadius.circular(2.r),
                              ),
                              child: Center(
                                child: Text(
                                  event.summary,
                                  style: TextStyle(
                                    fontSize: 8.sp,
                                    color: Colors.black87,
                                  ),
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ),
                            );
                          },
                        ),
                      ),
                  ],
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    if (calendarFormat == CalendarFormat.week) {
      return Container(
        height: 500.h,
        color: Colors.white,
        child: Column(
          mainAxisSize: MainAxisSize.max,
          children: [
            _buildWeekHeader(),
            Expanded(
              child: SingleChildScrollView(
                child: _buildTimeGrid(context),
              ),
            ),
          ],
        ),
      );
    }
    
    return TableCalendar<RequestModel>(
      firstDay: DateTime.now().subtract(const Duration(days: 365)),
      lastDay: DateTime.now().add(const Duration(days: 365)),
      focusedDay: focusedDay,
      selectedDayPredicate: (day) => isSameDay(selectedDay, day),
      eventLoader: eventLoader,
      onDaySelected: onDaySelected,
      calendarFormat: calendarFormat,
      rowHeight: calendarFormat == CalendarFormat.week ? 300 : 70, // Increased height for week view
      availableCalendarFormats: const {
        CalendarFormat.month: 'Month',
        CalendarFormat.week: 'Week',
      },
      calendarStyle: _calendarStyle,
      calendarBuilders: CalendarBuilders(
        defaultBuilder: (context, day, focusedDay) {
          final events = eventLoader(day);
          return Container(
            decoration: BoxDecoration(
              color: Colors.grey[50],
              border: Border.all(color: Colors.grey[200]!),
            ),
            child: calendarFormat == CalendarFormat.week 
                ? _buildWeekViewContent(context, day, events)
                : Column(
                    children: [
                      Align(
                        alignment: Alignment.topCenter,
                        child: Text(
                          '${day.day}',
                          style: const TextStyle(color: Colors.black),
                        ),
                      ),
                    ],
                  ),
          );
        },
        selectedBuilder: (context, day, focusedDay) {
          final events = eventLoader(day);
          return Container(
            decoration: BoxDecoration(
              color: Colors.blue[100],
              border: Border.all(color: Colors.blue),
            ),
            child: calendarFormat == CalendarFormat.week 
                ? _buildWeekViewContent(context, day, events)
                : Column(
                    children: [
                      Align(
                        alignment: Alignment.topCenter,
                        child: Text(
                          '${day.day}',
                          style: const TextStyle(color: Colors.black),
                        ),
                      ),
                    ],
                  ),
          );
        },
        todayBuilder: (context, day, focusedDay) {
          final events = eventLoader(day);
          return Container(
            decoration: BoxDecoration(
              color: Colors.grey[50],
              border: Border.all(color: Colors.blue),
            ),
            child: calendarFormat == CalendarFormat.week 
                ? _buildWeekViewContent(context, day, events)
                : Column(
                    children: [
                      Align(
                        alignment: Alignment.topCenter,
                        child: TextTitle14(
                          '${day.day}',
                          style: const TextStyle(color: Colors.black),
                        ),
                      ),
                    ],
                  ),
          );
        },
        markerBuilder: (context, date, events) {
          if (events.isEmpty || calendarFormat == CalendarFormat.week) return null;
          return _buildEventMarker(events.cast<RequestModel>(), context);
        },
      ),
      headerVisible: false,
      daysOfWeekHeight: 40,
      daysOfWeekStyle: const DaysOfWeekStyle(
        weekdayStyle: TextStyle(fontWeight: FontWeight.bold),
        weekendStyle: TextStyle(fontWeight: FontWeight.bold),
      ),
    );
  }

  Widget _buildWeekHeader() {
    return Container(
      height: 40.h,
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey[300]!),
        color: Colors.grey[50],
      ),
      child: Row(
        children: [
          SizedBox(width: 50.w),
          ...List.generate(7, (index) {
            final day = focusedDay.subtract(Duration(days: focusedDay.weekday - index - 1));
            return Expanded(
              child: Container(
                padding: EdgeInsets.all(2.h),
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey[300]!),
                ),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      DateFormat('EEE').format(day).toUpperCase(),
                      style: TextStyle(
                        fontSize: 10.sp,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Text(
                      day.day.toString(),
                      style: TextStyle(
                        fontSize: 12.sp,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
            );
          }),
        ],
      ),
    );
  }

  Widget _buildTimeGrid(BuildContext context) {
    const startHour = 0;
    const endHour = 23;
    const defaultScrollHour = 6; // Default starting hour (6 AM)
    final hours = List.generate(endHour - startHour + 1, (index) => startHour + index);
    final cellHeight = 60.h;
    // Create a scroll controller
    final scrollController = ScrollController(
      initialScrollOffset: defaultScrollHour * cellHeight,
    );
    // Ensure the scroll to 6 AM happens after build
    WidgetsBinding.instance.addPostFrameCallback((_) {
      scrollController.jumpTo(defaultScrollHour * cellHeight);
    });

    return SizedBox(
      height: MediaQuery.of(context).size.height * 0.7, // Limit height to 70% of screen
      child: SingleChildScrollView(
        controller: scrollController,
        child: IntrinsicHeight(
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Time column
              SizedBox(
                width: 50.w,
                child: Column(
                  children: hours.map((hour) {
                    return SizedBox(
                      height: cellHeight,
                      child: Center(
                        child: Text(
                          '${hour.toString().padLeft(2, '0')}:00',
                          style: TextStyle(
                            fontSize: 10.sp,
                            color: Colors.grey[600],
                          ),
                        ),
                      ),
                    );
                  }).toList(),
                ),
              ),
              // Days columns
              ...List.generate(7, (dayIndex) {
                final currentDay = focusedDay.subtract(
                  Duration(days: focusedDay.weekday - dayIndex - 1),
                );
                final dayEvents = eventLoader(currentDay);
                
                return Expanded(
                  child: SizedBox(
                    height: hours.length * cellHeight,
                    child: Stack(
                      children: [
                        // Hour grid lines
                        Column(
                          children: hours.map((hour) {
                            return Container(
                              height: cellHeight,
                              decoration: BoxDecoration(
                                border: Border.all(color: Colors.grey[200]!),
                              ),
                            );
                          }).toList(),
                        ),
                        // Events
                        ...dayEvents.map((event) {
                          final startHour = event.start.hour + (event.start.minute / 60);
                          final endHour = event.end.hour + (event.end.minute / 60);
                          final duration = endHour - startHour;
                          
                          return Positioned(
                            top: startHour * cellHeight,
                            left: 2.w,
                            right: 2.w,
                            height: max(duration * cellHeight, 40.h),
                            child: InkWell(
                              onTap: () => onShowAllEvents([event]),
                              child: Container(
                                decoration: BoxDecoration(
                                  color: Colors.blue[100],
                                  borderRadius: BorderRadius.circular(2.r),
                                  border: Border.all(color: Colors.blue),
                                ),
                                padding: EdgeInsets.all(2.h),
                                child: Builder(
                                  builder: (context) => Text(
                                    '${TimeOfDay.fromDateTime(event.start).format(context)} ${event.summary}', 
                                    style: const TextStyle(fontSize: 10)
                                  ),
                                ),
                              ),
                            ),
                          );
                        }),
                      ],
                    ),
                  ),
                );
              }),
            ],
          ),
        ),
      ),
    );
  }
}
