import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:the_voice_directory_flutter/core/theme/app_theme.dart';
import 'package:the_voice_directory_flutter/features/google_calender/widgets/date_time_selector.dart';
import 'package:the_voice_directory_flutter/utils/custom_toast.dart';
import 'package:the_voice_directory_flutter/utils/extensions/empty_space_extn.dart';
import 'package:the_voice_directory_flutter/utils/responsive.dart';
import 'package:the_voice_directory_flutter/utils/string_constants/app_images.dart';
import 'package:the_voice_directory_flutter/utils/string_constants/app_strings.dart';
import 'package:the_voice_directory_flutter/utils/validations.dart';
import 'package:the_voice_directory_flutter/widgets/buttons/primary_button.dart';
import 'package:the_voice_directory_flutter/widgets/common_circle_icon.dart';
import 'package:the_voice_directory_flutter/widgets/date_picker/date_picker_button.dart';
import 'package:the_voice_directory_flutter/widgets/textfields/app_textfield.dart';
import 'package:the_voice_directory_flutter/widgets/texts/app_text.dart';

class AddEventDialog extends StatefulWidget {
  final DateTime initialDate;
  final Duration defaultDuration;
  final Function(
          String title, DateTime start, DateTime end, List<String> emails, String eventLocation)
      onAddEvent;

  const AddEventDialog({
    super.key,
    required this.initialDate,
    this.defaultDuration = const Duration(hours: 1),
    required this.onAddEvent,
  });

  @override
  State<AddEventDialog> createState() => _AddEventDialogState();
}

class _AddEventDialogState extends State<AddEventDialog> {
  late final TextEditingController _titleController;
  late final TextEditingController _emailController;
  late final TextEditingController _eventLocationController;
  late DateTime _startDate;
  late DateTime _endDate;
  final List<String> _emails = [];

  @override
  void initState() {
    super.initState();
    _titleController = TextEditingController();
    _emailController = TextEditingController();
    _eventLocationController = TextEditingController();
    _startDate = widget.initialDate.toUtc();
    _endDate = _startDate; // Don't set default duration
  }

  @override
  void dispose() {
    _titleController.dispose();
    _emailController.dispose();
    _eventLocationController.dispose();
    super.dispose();
  }

  void _onStartDateChanged(DateTime newDate) {
    setState(() {
      _startDate = newDate.toUtc();
      // Ensure end date is on the same day
      _endDate = DateTime(
        newDate.year,
        newDate.month,
        newDate.day,
        _endDate.hour,
        _endDate.minute,
      ).toUtc();
    });
  }

  void _onEndDateChanged(DateTime newDate) {
    setState(() {
      _endDate = newDate.toUtc();
    });
  }

  void _addEmail() {
    final email = _emailController.text.trim();
    if (email.isEmpty) return;

    final emailError = Validator.emailValidator(email);
    if (emailError != null) {
      CustomToast.show(context: context, message: emailError);
      return;
    }

    if (_emails.contains(email)) {
      CustomToast.show(context: context, message: 'Email already added');
      return;
    }

    setState(() {
      _emails.add(email);
      _emailController.clear();
    });
  }

  void _removeEmail(String email) {
    setState(() {
      _emails.remove(email);
    });
  }

  Widget _buildEmailChips() {
    return Wrap(
        spacing: 8.0,
        children: _emails
            .map((email) => Chip(
                backgroundColor: Colors.white,
                label: Text(email, style: const TextStyle(fontSize: 12)),
                deleteIcon: const Icon(Icons.close, size: 16),
                onDeleted: () => _removeEmail(email)))
            .toList());
  }

  void _onSubmit() {
    String? validationError;

    if (_titleController.text.isEmpty) {
      validationError = AppStrings.enterEventTitle;
    } else if (_startDate == widget.initialDate.toUtc()) {
      validationError = AppStrings.selectStartTime;
    } else if (_endDate == widget.initialDate.toUtc()) {
      validationError = AppStrings.selectEndTime;
    } else if (_startDate == _endDate) {
      validationError = AppStrings.sameTime;
    } else if (_endDate.isBefore(_startDate)) {
      validationError = AppStrings.endTimeAfterStartTime;
    } else if(_eventLocationController.text.isEmpty){
      validationError = AppStrings.enterEventLocation;
    }

    if (validationError != null) {
      CustomToast.show( context: context,  message: validationError,isSuccess: false);
      return;
    }
    widget.onAddEvent( _titleController.text,_startDate,_endDate, _emails, _eventLocationController.text);
    context.pop();
  }

  @override
  Widget build(BuildContext context) {
    final localStartDate = _startDate.toLocal();
    final localEndDate = _endDate.toLocal();

    return AlertDialog(
      backgroundColor: Theme.of(context).colorScheme.white,
      titlePadding: EdgeInsets.zero,
      insetPadding: EdgeInsets.symmetric(
        horizontal: Responsive.isDesktop(context) ? 100.w : 24.w,
        vertical: 24.h,
      ),
      content: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Align(
              alignment: Alignment.centerRight,
              child: CommonCircleIcon(
                containerSize: 27.h,
                iconPath: AppImages.closeIcon,
                onTap: () => context.pop(),
              ),
            ),
            12.ph,
            TextTitle18And14(AppStrings.addTitle, fontWeight: FontWeight.w700),
            16.ph,
            AppTextFormField(
              controller: _titleController,
              hintText: AppStrings.enterEventTitle,
            ),
            16.ph,
            Align( alignment: Alignment.centerLeft, child: TextTitle18And14(AppStrings.selectDateAndTime, fontWeight: FontWeight.w700)),
            16.ph,
            Row(
              children: [
                Expanded(
                  child: DatePickerButton(
                    initialDate: _startDate.toLocal(),
                    onDateSelected: (date) {
                      _onStartDateChanged(DateTime(
                        date.year,
                        date.month,
                        date.day,
                        _startDate.hour,
                        _startDate.minute,
                      ));
                    },
                  ),
                ),
              ],
            ),
            20.ph,
            Row(
              children: [
                DateTimeSelector(
                  label: 'Start: ',
                  initialDate: localStartDate,
                  onDateSelected: _onStartDateChanged,
                ),
                12.pw,
                DateTimeSelector(
                  label: 'End: ',
                  initialDate: localEndDate,
                  onDateSelected: _onEndDateChanged,
                ),
              ],
            ),
            16.ph,
            Align( alignment: Alignment.centerLeft,
                child: TextTitle18And14('Add guests',fontWeight: FontWeight.w700)),
            16.ph,
            Row(
              children: [
                Expanded(
                  child: AppTextFormField(
                    controller: _emailController,
                    hintText: AppStrings.enterEmail,
                    keyboardType: TextInputType.emailAddress,
                    onSubmitted: (_) => _addEmail(),
                    inputFormatters: [
                      FilteringTextInputFormatter.deny(RegExp(r'\s')),
                    ],
                    suffixIcon: IconButton(icon: const Icon(Icons.add),
                      onPressed: _addEmail,
                    ),
                  ),
                ),
              ],
            ),
            if (_emails.isNotEmpty) ...[
              8.ph,
              _buildEmailChips(),
            ],
            12.ph,
            TextTitle18And14(AppStrings.eventLocation, fontWeight: FontWeight.w700),
            16.ph,
            AppTextFormField(
              controller: _eventLocationController,
              hintText: AppStrings.enterEventLocation,
            ),
          ],
        ),
      ),
      actions: [
        PrimaryButton(
            onPressed: _onSubmit,
            height: 45.h,
            width: 400.w,
            buttonText: AppStrings.save),
      ],
    );
  }
}
