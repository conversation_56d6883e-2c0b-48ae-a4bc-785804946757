import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import '../../../utils/custom_toast.dart';
import '../../../utils/extensions/empty_space_extn.dart';
import '../../../utils/responsive.dart';
import '../../../utils/string_constants/app_strings.dart';
import '../../../utils/validations.dart';
import '../../../widgets/dialogs.dart';
import '../../../widgets/textfields/app_textfield.dart';
import '../../../widgets/texts/app_text.dart';
import '../../job_details/bloc/job_detail_bloc.dart';
import '../../jobs/bloc/jobs_cubit.dart';
import '../../post_job/presentation/widgets/animated_rating_star.dart';
import '../../post_job/presentation/widgets/card_page_frame.dart';
import '../bloc/job_review_bloc.dart';

class JobReviewScreen extends StatefulWidget {
  final int jobId;
  final String name;

  const JobReviewScreen({
    super.key,
    required this.jobId,
    required this.name,
  });

  @override
  State<JobReviewScreen> createState() => _JobReviewScreenState();
}

class _JobReviewScreenState extends State<JobReviewScreen> {
  final _formKey = GlobalKey<FormState>();
  double ratingVal = 0;
  final TextEditingController reviewTextController = TextEditingController();
  bool autoValidation = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: BlocListener<JobReviewBloc, JobReviewState>(
        listener: (context, state) {
          if (state is JobReviewLoadingState) {
            Dialogs.showOnlyLoader(context);
          }
          if (state is JobReviewSuccessState) {
            context.pop();
            context.pop(true);
            context.read<JobDetailBloc>().getJobDetail(widget.jobId);
            context.read<JobsCubit>().fetchJobs();
            CustomToast.show(
              context: context,
              message: AppStrings.reviewSubmittedSuccessfully,
              isSuccess: true,
            );
            // context.pushReplacementNamed(
            //   RouteName.jobPayment,
            //   pathParameters: {
            //     Params.id: widget.jobId.toString(),
            //   },
            // );
            // context.read<JobDetailBloc>().getJobDetail(widget.jobId);
            // context.read<JobsCubit>().fetchJobs();
         }
          if (state is JobReviewErrorState) {
            context.pop();
            CustomToast.show(context: context, message: state.errorMessage);
          }
        },
        child: CardPageFrame(
          margin: Responsive.isDesktop(context)
              ? EdgeInsets.only(left: 120.w, right: 120.w, top: 28.h, bottom: 40.h)
              : EdgeInsets.symmetric(horizontal: 16.w),
          formKey: _formKey,
          onPressed: () {
            if (_formKey.currentState!.validate()) {
              if (ratingVal < 1) {
                CustomToast.show(context: context, message: AppStrings.selectStarRating);
                return;
              }
               Dialogs.showCommonDialog(
                  context: context,
                  title: AppStrings.markComplete.split(' ').map((word) => word[0].toUpperCase() + word.substring(1)).join(' '),
                  message: AppStrings.areYouSureYouWantSubmitReview,
                  onPrimaryButtonTap: () {
                    context.read<JobReviewBloc>().postJobReview(
                    jobId: widget.jobId,
                    feedback: reviewTextController.text,
                    rating: ratingVal.toInt(),
                  );
                  context.pop();
                  },
                  primaryButtonText: AppStrings.done,
                  showCancelButton: true,
                );
            } else {
              setState(() {
                autoValidation = true;
              });
            }
          },
          buttonText: AppStrings.complete,
          titleText: AppStrings.writeAReview,
          children: [
            TextDisplayLarge20And16(
              AppStrings.writeAReviewDescription(widget.name),
              fontSize: Responsive.isDesktop(context) ? 24.sp : 18.sp,
              fontWeight: Responsive.isDesktop(context) ? FontWeight.w700 : FontWeight.w500,
            ),
            Responsive.isDesktop(context) ? 36.ph : 20.ph,
            TextTitle18And14(
              AppStrings.addYourRating,
              fontSize: Responsive.isDesktop(context) ? 16.sp : 14.sp,
              fontWeight: FontWeight.w500,
            ),
            12.ph,
            AnimatedRatingStar(
              initialRating: ratingVal,
              minRating: 0,
              maxRating: 5,
              filledColor: Colors.amber,
              unfilledColor: Colors.grey[300],
              iconSize: Responsive.isDesktop(context) ? 44 : 33,
              animationDuration: const Duration(milliseconds: 200),
              allowHalfRating: false,
              onRatingUpdate: (newRating) {
                setState(() {
                  ratingVal = newRating;
                });
              },
              gap: Responsive.isDesktop(context) ? 24 : 12,
            ),
            Responsive.isDesktop(context) ? 32.ph : 36.ph,
            AppTextFormField(
              controller: reviewTextController,
              hintText: AppStrings.writeReviewHint,
              titleText: AppStrings.writeYourReview,
              maxLines: 5,
              keyboardType: TextInputType.multiline,
              textInputAction: TextInputAction.newline,
              isAutovalidateModeOn: autoValidation,
              maxLength: 1000,
              validator: (value) {
                return Validator.emptyValidator(value, ValidationMsg.plsEnter('review'));
              },
            ),
          ],
        ),
      ),
    );
  }
}
