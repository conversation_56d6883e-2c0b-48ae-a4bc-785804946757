import 'package:dotted_line/dotted_line.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:go_router/go_router.dart';
import 'package:the_voice_directory_flutter/core/api/api_params.dart';
import 'package:the_voice_directory_flutter/core/navigation/navigation_service_impl.dart';
import 'package:the_voice_directory_flutter/core/routes/route_names.dart';
import 'package:the_voice_directory_flutter/core/services/hive/hive_keys.dart';
import 'package:the_voice_directory_flutter/core/services/hive/hive_storage_helper.dart';
import 'package:the_voice_directory_flutter/core/theme/app_theme.dart';
import 'package:the_voice_directory_flutter/features/application_detail/bloc/accept_candidate_bloc.dart';
import 'package:the_voice_directory_flutter/features/application_detail/bloc/accept_candidate_state.dart';
import 'package:the_voice_directory_flutter/features/common/user_data/data/user_data_model.dart';
import 'package:the_voice_directory_flutter/features/application_detail/bloc/application_details_bloc.dart';
import 'package:the_voice_directory_flutter/features/application_detail/bloc/application_details_state.dart';
import 'package:the_voice_directory_flutter/features/application_detail/data/application_response_model.dart';
import 'package:the_voice_directory_flutter/features/voice/profile/presentation/error_screen.dart';
import 'package:the_voice_directory_flutter/utils/common_audio_player.dart';
import 'package:the_voice_directory_flutter/utils/common_shadow_container.dart';
import 'package:the_voice_directory_flutter/utils/date_formatter.dart';
import 'package:the_voice_directory_flutter/utils/environment_config.dart';
import 'package:the_voice_directory_flutter/utils/extensions/empty_space_extn.dart';
import 'package:the_voice_directory_flutter/utils/responsive.dart';
import 'package:the_voice_directory_flutter/utils/string_constants/app_images.dart';
import 'package:the_voice_directory_flutter/utils/string_constants/app_strings.dart';
import 'package:the_voice_directory_flutter/widgets/accept_candidate_dialog.dart';
import 'package:the_voice_directory_flutter/widgets/buttons/back_button.dart';
import 'package:the_voice_directory_flutter/widgets/common_circle_icon.dart';
import 'package:the_voice_directory_flutter/widgets/loading_dialog.dart';
import 'package:the_voice_directory_flutter/widgets/texts/app_text.dart';

import '../../../utils/custom_toast.dart';
import '../../../utils/get_profile_image.dart';
import '../../../utils/video_player_widget.dart';
import '../../../widgets/buttons/back_button_arrow.dart';
import '../../../widgets/buttons/primary_button.dart';
import '../../../widgets/dialogs.dart';
import '../../chat/bloc/chat_bloc.dart';
import '../../jobs/bloc/jobs_cubit.dart';
import '../../jobs/enums/application_status.dart';
import '../../jobs/enums/job_status.dart';
import '../bloc/shortlist_candidate_bloc.dart';

class ApplicationDetailScreen extends StatefulWidget {
  final int applicationId;
  const ApplicationDetailScreen({super.key, required this.applicationId});

  @override
  State<ApplicationDetailScreen> createState() =>
      _ApplicationDetailsScreenState();
}

class _ApplicationDetailsScreenState extends State<ApplicationDetailScreen> {
  UserDataModel? userDataModel;
  bool isClient = false;

  @override
  void initState() {
    userDataModel = HiveStorageHelper.getData<UserDataModel>(HiveBoxName.user, HiveKeys.userData);
    context
        .read<ApplicationDetailBloc>()
        .getApplicationDetail(widget.applicationId);
    super.initState();
  }

  String calculateTotalAmount(ApplicationResponseModel model) {
    final total = (model.quote ?? 0) * (1 + (model.platformFee ?? 0) / 100);
    return 'INR ${total.toStringAsFixed(2)}';
  }

  @override
  Widget build(BuildContext context) {
    ThemeData theme = Theme.of(context);
    ColorScheme colorScheme = theme.colorScheme;
    return Scaffold(
      body: SafeArea(
        child: BlocBuilder<ApplicationDetailBloc, ApplicationDetailsState>(
          builder: (context, state) {
            if (state is ApplicationLoadingState) {
              return const Center(child: Loader());
            }
            if (state is ApplicationErrorState) {
              return ErrorScreen(
                  onRetry: () {
                    context
                        .read<ApplicationDetailBloc>()
                        .getApplicationDetail(widget.applicationId);
                  },
                  errorMessage: state.errorMsg,
                  imageWidget: SvgPicture.asset(AppImages.snapMomentIcon,
                      height: 200, width: 100));
            }
            if (state is ApplicationSuccessState) {
              return BlocListener<ShortlistCandidateBloc,
                  ShortlistCandidateState>(
                listener: (context, state) {
                  if (state is ShortlistCandidateLoadingState) {
                    Dialogs.showOnlyLoader(context);
                  }
                  if (state is ShortlistCandidateSuccessState) {
                    CustomToast.show(
                      context: context,
                      message: AppStrings.candidateShortlistedSuccessfully,
                      isSuccess: true,
                    );
                    context.pop();
                    context.pop(true);
                  }
                  if (state is ShortlistCandidateErrorState) {
                    context.pop();
                    CustomToast.show(
                      context: context,
                      message: state.errorMsg,
                    );
                  }
                },
                child: SingleChildScrollView(
                  padding: EdgeInsets.symmetric(
                    horizontal: !Responsive.isDesktop(context) ? 0.h : 80.h,
                    vertical: !Responsive.isDesktop(context) ? 0.h : 40.h,
                  ),
                  child: ConstrainedBox(
                    constraints: BoxConstraints(
                      minHeight: MediaQuery.of(context).size.height,
                    ),
                    child: Column(
                      children: [
                        if (!!Responsive.isDesktop(context)) ...[
                          CustomBackButtonArrow(),
                          24.ph,
                          ],
                        Padding(
                          padding: EdgeInsets.symmetric(
                            horizontal:
                                !Responsive.isDesktop(context) ? 16.h : 0.h,
                            vertical:
                                !Responsive.isDesktop(context) ? 12 : 0.h,
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                children: [
                                  if (!Responsive.isDesktop(context))
                                    const Align(
                                      alignment: Alignment.centerLeft,
                                      child: CustomBackButton(),
                                    ),
                                  if (!!Responsive.isDesktop(context)) ...[
                                    TextDisplayLarge36And26(
                                        (userDataModel?.role == UserType.client || userDataModel?.role == UserType.ancillaryService)
                                            ? AppStrings.applicationDetails
                                            : AppStrings.myApplication),
                                    if (userDataModel?.role == UserType.client || userDataModel?.role == UserType.ancillaryService ) ...[
                                      const Spacer(),
                                      TextBodySmall12(
                                          '${AppStrings.dateOfApplication}: ${DateFormatter.formatDate(state.applicationResponseModel.createdAt)}'),
                                    ]
                                  ] else
                                    const Expanded(
                                      child: Center(
                                        child: TextDisplayLarge36And26(
                                            AppStrings.applicationDetails),
                                      ),
                                    ),
                                  SizedBox(
                                    height: 40.h,
                                    width: 40.h,
                                  )
                                ],
                              ),
                              if ((userDataModel?.role == UserType.client || userDataModel?.role == UserType.ancillaryService) &&
                                  !Responsive.isDesktop(context)) ...[
                                4.ph,
                                Center(
                                  child: TextBodySmall12(
                                      '${AppStrings.dateOfApplication}: ${DateFormatter.formatDate(state.applicationResponseModel.createdAt)}'),
                                ),
                              ],
                              if (userDataModel?.role == UserType.client || userDataModel?.role == UserType.ancillaryService) ...[
                                !Responsive.isDesktop(context) ? 20.ph : 24.ph,
                                CommonShadowContainer(
                                    child: Padding(
                                  padding: EdgeInsets.all(
                                      !Responsive.isDesktop(context)
                                          ? 12.h
                                          : 28.h),
                                  child: InkWell(
                                    onTap: () {
                                      NavigationServiceImpl.getInstance()!
                                          .doNavigation(
                                        context,
                                        routeName: RouteName.profile,
                                        pathParameters: {
                                          Params.id: state
                                                  .applicationResponseModel
                                                  .applicantsDetails
                                                  ?.id
                                                  .toString() ??
                                              '',
                                        },
                                      );
                                    },
                                    child: Row(
                                      children: [
                                        Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Text24And20SemiBold(
                                              AppStrings.applicantDetails,
                                              fontWeight: FontWeight.w600,
                                            ),
                                            !Responsive.isDesktop(context)
                                                ? 12.ph
                                                : 28.ph,
                                            Row(
                                              children: [
                                                SizedBox(
                                                  height: !Responsive.isDesktop(
                                                          context)
                                                      ? 44.h
                                                      : 62.h,
                                                  width: !Responsive.isDesktop(
                                                          context)
                                                      ? 44.h
                                                      : 62.h,
                                                  child: CircleAvatar(
                                                    radius: 60.r,
                                                    backgroundColor:
                                                        colorScheme
                                                            .blackE8E8E8,
                                                    backgroundImage: state
                                                                .applicationResponseModel
                                                                .applicantsDetails
                                                                ?.profilePic
                                                                ?.isNotEmpty ==
                                                            true
                                                        ? NetworkImage(
                                                            "${EnvironmentConfig.imageBaseUrl}${state.applicationResponseModel.applicantsDetails?.profilePic}")
                                                        : null,
                                                    child: state
                                                                .applicationResponseModel
                                                                .applicantsDetails
                                                                ?.profilePic
                                                                ?.isNotEmpty ==
                                                            true
                                                        ? null
                                                        : SvgPicture.asset(
                                                            "assets/images/user_icon.svg"),
                                                  ),
                                                ),
                                                16.pw,
                                                Text24And20SemiBold(
                                                  state
                                                          .applicationResponseModel
                                                          .applicantsDetails
                                                          ?.fullName ??
                                                      "",
                                                  fontWeight: FontWeight.w700,
                                                ),
                                              ],
                                            ),
                                          ],
                                        ),
                                        const Spacer(),
                                        CommonCircleIcon(
                                          iconPath: AppImages.rigtArrow,
                                          iconColor: colorScheme.black,
                                          iconSize: 15,
                                        ),
                                      ],
                                    ),
                                  ),
                                )),
                              ],
                              !Responsive.isDesktop(context) ? 20.ph : 24.ph,
                              CommonShadowContainer(
                                child: SizedBox(
                                  width: double.infinity,
                                  child: Padding(
                                    padding: EdgeInsets.symmetric(
                                        horizontal:
                                            !Responsive.isDesktop(context)
                                                ? 12.h
                                                : 28.h,
                                        vertical: !Responsive.isDesktop(context)
                                            ? 12.h
                                            : 28.h),
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        const Text24And20SemiBold(
                                          AppStrings.proposal,
                                          fontWeight: FontWeight.w600,
                                        ),
                                        !Responsive.isDesktop(context)
                                            ? 12.ph
                                            : 28.ph,
                                        ClickableText(
                                          text: state.applicationResponseModel
                                              .proposal
                                              .toString(),
                                          fontSizeMobile: 16,
                                          color: colorScheme.primaryGrey,
                                          fontWeightMobile: FontWeight.w500,
                                          fontSizeWeb: 20,
                                          maxLines: null,
                                          fontWeightWeb: FontWeight.w500,
                                          //  overflow: TextOverflow.ellipsis,
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                              ),
                              !Responsive.isDesktop(context) ? 20.ph : 24.ph,
                              CommonShadowContainer(
                                child: SizedBox(
                                  width: double.infinity,
                                  child: Padding(
                                    padding: EdgeInsets.symmetric(
                                        horizontal:
                                            !Responsive.isDesktop(context)
                                                ? 12.h
                                                : 28.h,
                                        vertical: !Responsive.isDesktop(context)
                                            ? 12.h
                                            : 28.h),
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Text24And20SemiBold(
                                          AppStrings.yourRevisionPolicy,
                                          fontWeight: FontWeight.w600,
                                        ),
                                        !Responsive.isDesktop(context)
                                            ? 12.ph
                                            : 28.ph,
                                        ClickableText(
                                          text: state
                                                      .applicationResponseModel
                                                      .revisionPolicy
                                                      ?.isNotEmpty ==
                                                  true
                                              ? state.applicationResponseModel
                                                  .revisionPolicy
                                                  .toString()
                                              : 'No revision policy',
                                          fontSizeMobile: 16,
                                          color: colorScheme.primaryGrey,
                                          fontWeightMobile: FontWeight.w500,
                                          fontSizeWeb: 20,
                                          fontWeightWeb: FontWeight.w500,
                                          maxLines: null,
                                          // overflow: TextOverflow.ellipsis,
                                        )
                                      ],
                                    ),
                                  ),
                                ),
                              ),
                              if ((state.applicationResponseModel.attachment ?? '').isNotEmpty) ...[
                                !Responsive.isDesktop(context) ? 20.ph : 24.ph,
                                CommonShadowContainer(
                                  child: SizedBox(
                                    width: double.infinity,
                                    child: Padding(
                                      padding: EdgeInsets.symmetric(
                                          horizontal:
                                              !Responsive.isDesktop(context)
                                                  ? 12.h
                                                  : 28.h,
                                          vertical:
                                              !Responsive.isDesktop(context)
                                              ? 12.h
                                              : 28.h),
                                      child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          Text24And20SemiBold(
                                            AppStrings.responseFile,
                                            fontWeight: FontWeight.w600,
                                          ),
                                          !Responsive.isDesktop(context)
                                              ? 12.ph
                                              : 28.ph,
                                          if (state.applicationResponseModel.attachment?.toLowerCase().endsWith('.mp4') == true) ...[
                                            VideoPlayerWidget(
                                              videoUrl: '${EnvironmentConfig.imageBaseUrl}${state.applicationResponseModel.attachment ?? ''}',
                                            ),
                                          ] else ...[
                                            CommonAudioPlayer(
                                              audioUrl: state.applicationResponseModel.attachment ?? '',
                                              colorScheme: colorScheme,
                                            ),
                                          ],
                                        ],
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                              !Responsive.isDesktop(context) ? 20.ph : 24.ph,
                              CommonShadowContainer(
                                child: Padding(
                                  padding: EdgeInsets.symmetric(
                                      horizontal: !Responsive.isDesktop(context)
                                          ? 12.h
                                          : 28.h,
                                      vertical: !Responsive.isDesktop(context)
                                          ? 12.h
                                          : 28.h),
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text24And20SemiBold(
                                        AppStrings.quote,
                                        fontWeight: FontWeight.w600,
                                      ),
                                      !Responsive.isDesktop(context)
                                          ? 12.ph
                                          : 28.ph,
                                      Row(
                                        children: [
                                          const TextTitle18And14(AppStrings.price),
                                          const Spacer(),
                                          TextTitle18And14('INR ${state.applicationResponseModel.quote.toString()}'),
                                        ],
                                      ),
                                      // !Responsive.isDesktop(context)
                                      //     ? 8.ph
                                      //     : 16.ph,
                                      // Row(
                                      //   children: [
                                      //     TextTitle18And14(
                                      //       AppStrings.platformFee,
                                      //       color: colorScheme
                                      //           .hyperlinkBlueColor,
                                      //     ),
                                      //     const Spacer(),
                                      //     TextTitle18And14(
                                      //       '${state.applicationResponseModel.platformFee?.toString()}%',
                                      //       color: colorScheme
                                      //           .hyperlinkBlueColor,
                                      //     ),
                                      //   ],
                                      // ),
                                      // !Responsive.isDesktop(context)
                                      //     ? 12.ph
                                      //     : 20.ph,
                                      // DottedLine(
                                      //   dashLength: 9,
                                      //   dashColor:
                                      //       colorScheme.lightGreyD9D9D9,
                                      //   lineThickness: 1,
                                      // ),
                                      // !Responsive.isDesktop(context)
                                      //     ? 12.ph
                                      //     : 20.ph,
                                      // Row(
                                      //   children: [
                                      //     const TextTitle18And14(
                                      //         AppStrings.totalPrice),
                                      //     const Spacer(),
                                      //     TextTitle18And14(
                                      //         calculateTotalAmount(state
                                      //             .applicationResponseModel)),
                                      //   ],
                                      // ),
                                    ],
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                        if ((userDataModel?.role == UserType.client || userDataModel?.role == UserType.ancillaryService) &&
                            state.applicationResponseModel.applicationStatus == ApplicationStatus.shortlisted) ...[
                          Responsive.isDesktop(context)
                              ? 25.ph
                              : SizedBox.shrink(),
                          if (Responsive.isDesktop(context)) ...[
                            Divider(
                              color: colorScheme.lightGreyD9D9D9,
                              thickness: 0.5,
                            ),
                          ],
                          Padding(
                            padding: EdgeInsets.symmetric(
                                horizontal:
                                    !Responsive.isDesktop(context) ? 16 : 206.w,
                                vertical: 20.h),
                            child: Row(
                              children: [
                                Expanded(
                                  child: PrimaryButton(
                                    backgroundColor: colorScheme.lightGreenFDFFDA,
                                    onPressed: () {
                                      final idFrom = userDataModel?.id;
                                      final idTo = state.applicationResponseModel.voice;
                                      final jobId = state.applicationResponseModel.job;
                                      if (idFrom == null || idTo == null || jobId == null) {
                                        CustomToast.show(context: context, message: AppStrings.genericErrorMsg);
                                        return;
                                      }
                                      String groupId = '';
                                      if (idFrom < idTo) {
                                        groupId = "${jobId}_${idFrom}_$idTo";
                                      } else {
                                        groupId = "${jobId}_${idTo}_$idFrom";
                                      }
                                      String? myName = '${userDataModel?.firstName ?? ''} ${userDataModel?.lastName ?? ''}';
                                      String? myProfileImg = ProfileImage.getProfileImage(userDataModel?.profilePic);
                                      String? voiceProfileImg = ProfileImage.getProfileImage(state.applicationResponseModel.applicantsDetails?.profilePic);
                                      context.read<ChatBloc>().updateChatState(selectedChat: groupId, isFromChatList: false, isJobChat: true);
                                      NavigationServiceImpl.getInstance()!.doNavigation(context, routeName: RouteName.chat,
                                        pathParameters: {
                                          Params.jobName: state.applicationResponseModel.jobTitle ?? '',
                                          Params.groupId: groupId,
                                          Params.jobId: jobId.toString(),
                                          Params.idFrom: idFrom.toString(),
                                          Params.idTo: idTo.toString(),
                                          Params.myName: myName,
                                          Params.myProfileImg: myProfileImg,
                                          Params.name: state.applicationResponseModel.applicantsDetails?.fullName ?? '',
                                          Params.profileImg: voiceProfileImg,
                                          Params.needBackBtn: 'true',
                                        },
                                      );
                                    },
                                    shape: RoundedRectangleBorder(
                                      borderRadius:
                                          BorderRadius.circular(40.r),
                                      side: BorderSide(
                                        color: colorScheme.primary,
                                        width: 1.2.h,
                                      ),
                                    ),
                                    child: const TextTitle18And14(
                                      AppStrings.chat,
                                    ),
                                  ),
                                ),
                                if (state.applicationResponseModel.jobStatus != JobStatus.closed &&
                                    state.applicationResponseModel.jobStatus != JobStatus.inProgress) ...[
                                  16.pw,
                                  Expanded(
                                    child: BlocListener<AcceptCandidateBloc,
                                        AcceptCandidateState>(
                                      listener: (context, state) {
                                        if (state
                                            is AcceptCandidateLoadingState) {
                                          Dialogs.showOnlyLoader(context);
                                        }
                                        if (state
                                            is AcceptCandidateSuccessState) {
                                          CustomToast.show(
                                            context: context,
                                            message: AppStrings
                                                .candidateAcceptedSuccessfully,
                                            isSuccess: true,
                                          );
                                          context.pop();
                                          context.read<JobsCubit>().fetchJobs();
                                          context.pop(true);
                                        }
                                        if (state
                                            is AcceptCandidateErrorState) {
                                          context.pop();
                                          CustomToast.show(
                                            context: context,
                                            message: state.errorMsg,
                                          );
                                        }
                                      },
                                      child: PrimaryButton(
                                        onPressed: () {
                                          if (state.applicationResponseModel.isJobAccepted == true) {
                                            CustomToast.show(
                                              context: context,
                                              message: AppStrings.candidateAlreadyAccepted,
                                            );
                                            return;
                                          }
                                          _acceptCandidateConfirmationDialog(
                                            colorScheme: colorScheme,
                                            context: context,
                                            amount: state
                                                    .applicationResponseModel
                                                    .quote ??
                                                0,
                                            applicationId: widget.applicationId,
                                          );
                                        },
                                        buttonText: AppStrings.accept,
                                      ),
                                    ),
                                  )
                                ],
                              ],
                            ),
                          )
                        ],
                        if (state.applicationResponseModel.jobStatus != JobStatus.closed &&
                            state.applicationResponseModel.jobStatus != JobStatus.inProgress &&
                            (userDataModel?.role == UserType.client || userDataModel?.role == UserType.ancillaryService) &&
                            state.applicationResponseModel.applicationStatus == ApplicationStatus.applied) ...[
                          Responsive.isDesktop(context)
                              ? 25.ph
                              : SizedBox.shrink(),
                          if (Responsive.isDesktop(context)) ...[
                            Divider(
                              color: colorScheme.lightGreyD9D9D9,
                              thickness: 0.5,
                            ),
                          ],
                          Padding(
                            padding: EdgeInsets.symmetric(
                              horizontal: Responsive.isDesktop(context)
                                  ? 206.w
                                  : 16.w,
                              vertical:
                                  Responsive.isDesktop(context) ? 20.h : 20.h,
                            ),
                            child: Center(
                              child: PrimaryButton(
                                buttonText: AppStrings.shortlist,
                                onPressed: () {
                                  if (state.applicationResponseModel.isJobAccepted == true) {
                                    CustomToast.show(
                                      context: context,
                                      message: AppStrings.candidateAlreadyAccepted,
                                    );
                                    return;
                                  }
                                  context
                                      .read<ShortlistCandidateBloc>()
                                      .shortlistCandidateApplication(
                                          widget.applicationId);
                                },
                              ),
                            ),
                          ),
                        ],
                      ],
                    ),
                  ),
                ),
              );
            }
            return const SizedBox();
          },
        ),
      ),
    );
  }
}

void _acceptCandidateConfirmationDialog(
    {required ColorScheme colorScheme,
    required BuildContext context,
    required int applicationId,
    required double amount}) {
  AcceptCandidateDialog.showAcceptCandidateDialog(
    context: context,
    title: AppStrings.areYouSureYouWantToAcceptThisApplication,
    message: AppStrings.onceYouAcceptThisApplication,
    amount: amount,
    primaryButtonText: AppStrings.accept,
    primaryButtonColor: colorScheme.primaryGrey,
    cancelButtonText: AppStrings.cancel,
    onPrimaryButtonTap: () async {
      context.read<AcceptCandidateBloc>().acceptShortlistedApplication(applicationId);
      context.pop();
    },
  );
}
